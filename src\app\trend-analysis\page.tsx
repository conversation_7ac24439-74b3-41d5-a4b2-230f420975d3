"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import HittingTrendAnalysis from '@/components/dashboard/Hitting/HittingTrendAnalysis';
import { useSearchParams } from 'next/navigation';
import AdvancedMetricsPanel from '@/components/dashboard/Hitting/AdvancedMetricsPanel';

export default function TrendAnalysis() {
  const router = useRouter();
  const [selectedMetric, setSelectedMetric] = useState<string>('overall');
  const [timeRange, setTimeRange] = useState<string>('30');
  const searchParams = useSearchParams();
const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('College');
const [selectedBatter, setSelectedBatter] = useState<string | null>(null);
const [data, setData] = useState<any[]>([]);

// Get data from sessionStorage on component mount
React.useEffect(() => {
    const storedData = sessionStorage.getItem('hittingData');
    const storedBatter = sessionStorage.getItem('selectedBatter');
    const storedLevel = sessionStorage.getItem('selectedLevel');

    if (storedData) {
      setData(JSON.parse(storedData));
    }
    if (storedBatter) {
      setSelectedBatter(storedBatter);
    }
    if (storedLevel) {
      setSelectedLevel(storedLevel as 'Professional' | 'College' | 'High School');
    }
  }, []);


  return (
    <div className="min-h-screen w-full p-6" style={{ backgroundColor: 'var(--dashboard-bg)' }}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
        <button
            onClick={() => {
                // Just go back to dashboard - data will be lost but that's what you want
                router.push(`/?view=hitting`);
            }}
            className="btn-primary flex items-center gap-2"
            >
            <ArrowLeft className="w-4 h-4" />
            Back to Dashboard
            </button>

          <div className="flex gap-4">
            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger className="select-trigger w-[200px]">
                <SelectValue placeholder="Select Metric" />
              </SelectTrigger>
              <SelectContent className="select-content">
                <SelectItem className="select-item" value="overall">Overall Score</SelectItem>
                <SelectItem className="select-item" value="qualityOfContact">Quality of Contact</SelectItem>
                <SelectItem className="select-item" value="roomForError">Room for Error</SelectItem>
                <SelectItem className="select-item" value="launchAngle">Launch Angle</SelectItem>
              </SelectContent>
            </Select>

            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="select-trigger w-[200px]">
                <SelectValue placeholder="Select Time Range" />
              </SelectTrigger>
              <SelectContent className="select-content">
                <SelectItem className="select-item" value="7">Last 7 Days</SelectItem>
                <SelectItem className="select-item" value="30">Last 30 Days</SelectItem>
                <SelectItem className="select-item" value="90">Last 90 Days</SelectItem>
                <SelectItem className="select-item" value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
            <div className="text-center mb-6">
            <h1 className="text-3xl font-bold text-white">
                {selectedBatter ? `${selectedBatter}'s Performance Trends` : 'Performance Trends'}
            </h1>
            </div>

        {/* Main Content */}
        <div className="floating-card p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Hitting Trend Analysis</h3>
          <div className="h-[600px]">
            {data.length > 0 && selectedBatter && (
                    <HittingTrendAnalysis
                        data={data.map(hit => ({ ...hit, Level: selectedLevel }))}  // Add the level to each hit
                        selectedBatter={selectedBatter}
                        selectedLevel={selectedLevel}
                    />
                    )}
            </div>
        </div>
            <AdvancedMetricsPanel
        data={data}
        selectedBatter={selectedBatter || ''}
    />
    </div>
    </div>
  );
}