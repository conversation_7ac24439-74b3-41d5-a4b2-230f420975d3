{"name": "blp3.0", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@tanstack/react-query": "^5.62.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "next": "^13.4.10", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^2.5.0", "tailwind-merge": "^2.5.4", "typescript": "^5.1.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@next/bundle-analyzer": "^14.0.3", "cross-env": "^7.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.1", "@types/node": "20.3.1", "@types/react": "18.2.6", "@types/react-dom": "18.2.4", "autoprefixer": "^10.4.20", "eslint": "8.43.0", "eslint-config-next": "13.4.10", "postcss": "^8.4.49", "tailwindcss": "^3.4.16"}}