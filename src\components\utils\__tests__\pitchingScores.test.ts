import { 
  calculateSinglePitchScore, 
  calculateArsenalScore,
  LEVEL_BENCHMARKS,
  DEFAULT_PITCH_SCORE_PARAMS,
  DEFAULT_ARSENAL_SCORE_PARAMS,
  getPitchTypeColor
} from '../pitchingScores';

// Mock pitch data for testing
const mockPitchData = {
  Pitcher: 'Test Pitcher',
  AutoPitchType: 'FF',
  RelSpeed: 92,
  SpinRate: 2250,
  VertBreak: 15,
  HorzBreak: 7,
  RelSide: 2,
  RelHeight: 6,
  Extension: 6.5
};

const mockPitcherData = [
  mockPitchData,
  { ...mockPitchData, RelSpeed: 91, SpinRate: 2200 },
  { ...mockPitchData, RelSpeed: 93, SpinRate: 2300 }
];

describe('pitchingScores utilities', () => {
  describe('calculateSinglePitchScore', () => {
    it('should calculate correct scores for benchmark pitch', () => {
      const benchmarks = LEVEL_BENCHMARKS.Professional.FF;
      const result = calculateSinglePitchScore(
        mockPitchData,
        benchmarks,
        mockPitcherData,
        DEFAULT_PITCH_SCORE_PARAMS
      );

      expect(result).toHaveProperty('velocityScore');
      expect(result).toHaveProperty('movementScore');
      expect(result).toHaveProperty('spinScore');
      expect(result).toHaveProperty('releaseScore');
      expect(result).toHaveProperty('totalScore');

      expect(result.velocityScore).toBeGreaterThan(0);
      expect(result.totalScore).toBeGreaterThan(0);
      expect(result.totalScore).toBeLessThanOrEqual(100);
    });

    it('should handle above-benchmark velocity correctly', () => {
      const benchmarks = LEVEL_BENCHMARKS.Professional.FF;
      const fastPitch = { ...mockPitchData, RelSpeed: 98 };
      
      const result = calculateSinglePitchScore(
        fastPitch,
        benchmarks,
        mockPitcherData,
        DEFAULT_PITCH_SCORE_PARAMS
      );

      expect(result.velocityScore).toBeGreaterThan(70);
    });

    it('should handle below-benchmark velocity correctly', () => {
      const benchmarks = LEVEL_BENCHMARKS.Professional.FF;
      const slowPitch = { ...mockPitchData, RelSpeed: 85 };
      
      const result = calculateSinglePitchScore(
        slowPitch,
        benchmarks,
        mockPitcherData,
        DEFAULT_PITCH_SCORE_PARAMS
      );

      expect(result.velocityScore).toBeLessThan(70);
    });

    it('should calculate release score based on consistency', () => {
      const benchmarks = LEVEL_BENCHMARKS.Professional.FF;
      const consistentData = [
        { ...mockPitchData, RelSide: 2.0, RelHeight: 6.0 },
        { ...mockPitchData, RelSide: 2.1, RelHeight: 6.1 },
        { ...mockPitchData, RelSide: 1.9, RelHeight: 5.9 }
      ];
      
      const result = calculateSinglePitchScore(
        mockPitchData,
        benchmarks,
        consistentData,
        DEFAULT_PITCH_SCORE_PARAMS
      );

      expect(result.releaseScore).toBeGreaterThan(90);
    });
  });

  describe('calculateArsenalScore', () => {
    it('should return 0 for single pitch type', () => {
      const result = calculateArsenalScore([mockPitchData]);
      expect(result).toBe(0);
    });

    it('should calculate score for multiple pitch types', () => {
      const multiPitchData = [
        { ...mockPitchData, AutoPitchType: 'FF', RelSpeed: 92 },
        { ...mockPitchData, AutoPitchType: 'SL', RelSpeed: 86 },
        { ...mockPitchData, AutoPitchType: 'CH', RelSpeed: 85 }
      ];

      const result = calculateArsenalScore(multiPitchData, DEFAULT_ARSENAL_SCORE_PARAMS);
      
      expect(result).toBeGreaterThan(0);
      expect(result).toBeLessThanOrEqual(100);
    });

    it('should penalize poor velocity separation', () => {
      const poorSeparation = [
        { ...mockPitchData, AutoPitchType: 'FF', RelSpeed: 92 },
        { ...mockPitchData, AutoPitchType: 'SL', RelSpeed: 91 } // Too close
      ];

      const goodSeparation = [
        { ...mockPitchData, AutoPitchType: 'FF', RelSpeed: 92 },
        { ...mockPitchData, AutoPitchType: 'SL', RelSpeed: 84 } // Good separation
      ];

      const poorScore = calculateArsenalScore(poorSeparation);
      const goodScore = calculateArsenalScore(goodSeparation);

      expect(goodScore).toBeGreaterThan(poorScore);
    });
  });

  describe('getPitchTypeColor', () => {
    it('should return correct colors for known pitch types', () => {
      expect(getPitchTypeColor('FF')).toBe('#e63946');
      expect(getPitchTypeColor('SL')).toBe('#1d3557');
      expect(getPitchTypeColor('CH')).toBe('#2a9d8f');
      expect(getPitchTypeColor('CU')).toBe('#6b705c');
    });

    it('should return default color for unknown pitch types', () => {
      expect(getPitchTypeColor('UNKNOWN')).toBe('#000000');
    });
  });

  describe('LEVEL_BENCHMARKS', () => {
    it('should have benchmarks for all levels', () => {
      expect(LEVEL_BENCHMARKS).toHaveProperty('Professional');
      expect(LEVEL_BENCHMARKS).toHaveProperty('College');
      expect(LEVEL_BENCHMARKS).toHaveProperty('High School');
    });

    it('should have consistent pitch types across levels', () => {
      const levels = Object.keys(LEVEL_BENCHMARKS);
      const pitchTypes = Object.keys(LEVEL_BENCHMARKS[levels[0]]);

      levels.forEach(level => {
        pitchTypes.forEach(pitchType => {
          expect(LEVEL_BENCHMARKS[level]).toHaveProperty(pitchType);
          expect(LEVEL_BENCHMARKS[level][pitchType]).toHaveProperty('velocity');
          expect(LEVEL_BENCHMARKS[level][pitchType]).toHaveProperty('spinRate');
          expect(LEVEL_BENCHMARKS[level][pitchType]).toHaveProperty('vertBreak');
          expect(LEVEL_BENCHMARKS[level][pitchType]).toHaveProperty('horzBreak');
        });
      });
    });

    it('should have realistic velocity ranges', () => {
      Object.values(LEVEL_BENCHMARKS).forEach(level => {
        Object.values(level).forEach(benchmark => {
          expect(benchmark.velocity).toBeGreaterThan(60);
          expect(benchmark.velocity).toBeLessThan(110);
        });
      });
    });
  });

  describe('DEFAULT_PITCH_SCORE_PARAMS', () => {
    it('should have all required parameters', () => {
      expect(DEFAULT_PITCH_SCORE_PARAMS).toHaveProperty('weight_velocity');
      expect(DEFAULT_PITCH_SCORE_PARAMS).toHaveProperty('weight_movement');
      expect(DEFAULT_PITCH_SCORE_PARAMS).toHaveProperty('weight_spin');
      expect(DEFAULT_PITCH_SCORE_PARAMS).toHaveProperty('weight_release_point');
    });

    it('should have weights that sum to reasonable values', () => {
      const totalWeight = DEFAULT_PITCH_SCORE_PARAMS.weight_velocity +
                         DEFAULT_PITCH_SCORE_PARAMS.weight_movement +
                         DEFAULT_PITCH_SCORE_PARAMS.weight_spin +
                         DEFAULT_PITCH_SCORE_PARAMS.weight_release_point;
      
      expect(totalWeight).toBeCloseTo(1.0, 1);
    });
  });
});
