import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScoreGauge } from '../PitchingMetricsDashboard';
import { 
  calculateTotalHitScore,
  calculateQualityOfContactScore,
  calculateRoomForErrorScore,
  calculateLaunchAngleScore,
  LEVEL_BENCHMARKS,
  DEFAULT_PARAMS
} from '../../utils/calculateScores';

interface HittingData {
  Batter: string;
  BatterSide: string;
  ExitSpeed: number;
  Angle: number;
  Direction: number;
  HitSpinRate: number;
  HitSpinAxis: number;
  Distance: number;
  Bearing: number;
  HangTime: number;
  Level?: 'Professional' | 'College' | 'High School';
  EventID?: string;
  'Event Date'?: string;
  'Event Location'?: string;
  'Event Type'?: string;
  'Session ID'?: string;
  'Next Event'?: string;
}

interface EventTimelineProps {
  data: HittingData[];
  selectedBatter: string;
  selectedLevel: 'Professional' | 'College' | 'High School';
  selectedEvent: string | null;
  onEventSelect: (eventId: string | null) => void;
}

const EventTimeline: React.FC<EventTimelineProps> = ({
  data,
  selectedBatter,
  selectedLevel,
  selectedEvent,
  onEventSelect
}) => {
  const events = data
    .filter(hit => hit.Batter === selectedBatter)
    .filter((hit, index, self) =>
      index === self.findIndex((h) => h.EventID === hit.EventID && hit.EventID)
    )
    .sort((a, b) => (a["Event Date"] || '').localeCompare(b["Event Date"] || ''));

  const selectedEventData = data.find(hit => hit.EventID === selectedEvent);
  const eventHits = data.filter(hit => hit.EventID === selectedEvent);

  return (
    <>
      <Card className="bg-white backdrop-blur shadow-lg mt-4 border border-black">
        <CardHeader>
          <CardTitle className="text-black">Event Timeline</CardTitle>
        </CardHeader>
        <CardContent className="pt-6 text-black">
          <div className="relative py-8">
            <div className="absolute left-0 top-1/2 w-full h-0.5 bg-gray-200"></div>
            <div className="relative flex justify-between px-4">
              {events.map((event, index) => (
                <div key={index} className="flex flex-col items-center w-32">
                  <div
                    onClick={() => onEventSelect(event.EventID || null)}
                    className={`w-5 h-5 rounded-full border-2 border-white shadow-md ${
                      event["Event Type"]?.includes('Baseline')
                        ? 'bg-blue-500 hover:bg-blue-600'
                        : 'bg-green-500 hover:bg-green-600'
                    } ${
                      event.EventID === selectedEvent ? 'ring-2 ring-offset-2 ring-black' : ''
                    } z-10 cursor-pointer transition-colors duration-200`}
                  ></div>
                  <div className="mt-2 text-sm font-medium text-center w-full">
                    <div className="font-semibold truncate">{event["Event Type"]}</div>
                    <div className="text-xs text-black-500">{event["Event Date"]}</div>
                    <div className="text-xs text-black-400">{event["Event Location"]}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {selectedEvent && selectedEventData && (
        <Card className="bg-white backdrop-blur border border-black shadow-lg mt-4">
          <CardContent className="pt-6">
            <div className="grid grid-cols-3 gap-4">
              {/* Left column - Event Details */}
              <div className="col-span-1">
                <h3 className="font-semibold text-lg mb-2 text-black">
                  {selectedEventData["Event Type"]}
                </h3>
                <div className="space-y-1">
                  <p className="text-sm text-gray-900">
                    <span className="font-medium">Date:</span> {selectedEventData["Event Date"]}
                  </p>
                  <p className="text-sm text-gray-900">
                    <span className="font-medium">Location:</span> {selectedEventData["Event Location"]}
                  </p>
                  <p className="text-sm text-gray-900">
                    <span className="font-medium">Session ID:</span> {selectedEventData["Session ID"]}
                  </p>
                </div>
              </div>

              {/* Center column - Score Gauges */}
              <div className="col-span-2">
                <div className="grid grid-cols-2 gap-6 max-w-md mx-auto">
                  <div className="flex flex-col items-center">
                    <ScoreGauge
                      score={calculateQualityOfContactScore(
                        selectedEventData,
                        LEVEL_BENCHMARKS[selectedLevel]
                      )}
                      size="md"
                      label="Quality of Contact"
                    />
                  </div>
                  <div className="flex flex-col items-center">
                    <ScoreGauge
                      score={calculateRoomForErrorScore(
                        eventHits,
                        eventHits.length,
                        DEFAULT_PARAMS.roomForError
                      )}
                      size="md"
                      label="Room for Error"
                    />
                  </div>
                  <div className="flex flex-col items-center">
                    <ScoreGauge
                      score={calculateLaunchAngleScore(
                        selectedEventData,
                        DEFAULT_PARAMS.launchAngle
                      )}
                      size="md"
                      label="Launch Angle"
                    />
                  </div>
                  <div className="flex flex-col items-center">
                    <ScoreGauge
                      score={calculateTotalHitScore(
                        selectedEventData,
                        eventHits.length,
                        eventHits
                      )}
                      size="md"
                      label="Event Score"
                    />
                  </div>
                </div>
                <div className="flex justify-center mt-6">
                  <Button
                    onClick={() => onEventSelect(null)}
                    variant="outline"
                    className="bg-white/80 text-black border-black/20"
                  >
                    Close Details
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};

export default EventTimeline;
