// Data validation utilities for baseball analytics

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface PitchDataValidation {
  Pitcher: string;
  AutoPitchType: string;
  RelSpeed: number;
  SpinRate: number;
  VertBreak: number;
  HorzBreak: number;
  RelSide: number;
  RelHeight: number;
  Extension: number;
}

export interface HittingDataValidation {
  Batter: string;
  BatterSide: string;
  ExitSpeed: number;
  Angle: number;
  Direction: number;
  HitSpinRate: number;
  HitSpinAxis: number;
  Distance: number;
}

// Validation rules for pitching data
export const PITCH_VALIDATION_RULES = {
  RelSpeed: { min: 40, max: 110, required: true },
  SpinRate: { min: 0, max: 4000, required: true },
  VertBreak: { min: -30, max: 30, required: true },
  HorzBreak: { min: -30, max: 30, required: true },
  RelSide: { min: -5, max: 5, required: true },
  RelHeight: { min: 0, max: 10, required: true },
  Extension: { min: 0, max: 10, required: true },
  Pitcher: { required: true, type: 'string' },
  AutoPitchType: { required: true, type: 'string' },
} as const;

// Validation rules for hitting data
export const HITTING_VALIDATION_RULES = {
  ExitSpeed: { min: 0, max: 130, required: true },
  Angle: { min: -90, max: 90, required: true },
  Direction: { min: -180, max: 180, required: false },
  HitSpinRate: { min: 0, max: 6000, required: false },
  HitSpinAxis: { min: 0, max: 360, required: false },
  Distance: { min: 0, max: 600, required: false },
  Batter: { required: true, type: 'string' },
  BatterSide: { required: false, type: 'string' },
} as const;

export function validatePitchData(data: any[]): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!Array.isArray(data) || data.length === 0) {
    errors.push('No data provided or data is not an array');
    return { isValid: false, errors, warnings };
  }

  data.forEach((row, index) => {
    // Check required fields
    Object.entries(PITCH_VALIDATION_RULES).forEach(([field, rules]) => {
      if (rules.required && (row[field] === undefined || row[field] === null || row[field] === '')) {
        errors.push(`Row ${index + 1}: Missing required field '${field}'`);
      }

      if (row[field] !== undefined && row[field] !== null && row[field] !== '') {
        // Type validation
        if (rules.type === 'string' && typeof row[field] !== 'string') {
          errors.push(`Row ${index + 1}: Field '${field}' must be a string`);
        }

        // Numeric range validation
        if ('min' in rules && 'max' in rules) {
          const value = Number(row[field]);
          if (isNaN(value)) {
            errors.push(`Row ${index + 1}: Field '${field}' must be a number`);
          } else {
            if (value < rules.min || value > rules.max) {
              warnings.push(`Row ${index + 1}: Field '${field}' value ${value} is outside expected range (${rules.min}-${rules.max})`);
            }
          }
        }
      }
    });

    // Business logic validations
    if (row.RelSpeed && row.RelSpeed < 60) {
      warnings.push(`Row ${index + 1}: Unusually low velocity (${row.RelSpeed} mph)`);
    }
    if (row.SpinRate && row.SpinRate > 3500) {
      warnings.push(`Row ${index + 1}: Unusually high spin rate (${row.SpinRate} rpm)`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

export function validateHittingData(data: any[]): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!Array.isArray(data) || data.length === 0) {
    errors.push('No data provided or data is not an array');
    return { isValid: false, errors, warnings };
  }

  data.forEach((row, index) => {
    // Check required fields
    Object.entries(HITTING_VALIDATION_RULES).forEach(([field, rules]) => {
      if (rules.required && (row[field] === undefined || row[field] === null || row[field] === '')) {
        errors.push(`Row ${index + 1}: Missing required field '${field}'`);
      }

      if (row[field] !== undefined && row[field] !== null && row[field] !== '') {
        // Type validation
        if (rules.type === 'string' && typeof row[field] !== 'string') {
          errors.push(`Row ${index + 1}: Field '${field}' must be a string`);
        }

        // Numeric range validation
        if ('min' in rules && 'max' in rules) {
          const value = Number(row[field]);
          if (isNaN(value)) {
            errors.push(`Row ${index + 1}: Field '${field}' must be a number`);
          } else {
            if (value < rules.min || value > rules.max) {
              warnings.push(`Row ${index + 1}: Field '${field}' value ${value} is outside expected range (${rules.min}-${rules.max})`);
            }
          }
        }
      }
    });

    // Business logic validations
    if (row.ExitSpeed && row.ExitSpeed < 50) {
      warnings.push(`Row ${index + 1}: Unusually low exit velocity (${row.ExitSpeed} mph)`);
    }
    if (row.ExitSpeed && row.ExitSpeed > 120) {
      warnings.push(`Row ${index + 1}: Unusually high exit velocity (${row.ExitSpeed} mph)`);
    }
    if (row.Angle && Math.abs(row.Angle) > 60) {
      warnings.push(`Row ${index + 1}: Extreme launch angle (${row.Angle}°)`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// CSV header validation
export function validateCSVHeaders(headers: string[], expectedHeaders: string[]): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const normalizedHeaders = headers.map(h => h.trim().toLowerCase());
  const normalizedExpected = expectedHeaders.map(h => h.toLowerCase());

  // Check for missing required headers
  const missingHeaders = normalizedExpected.filter(expected => 
    !normalizedHeaders.some(header => header === expected)
  );

  if (missingHeaders.length > 0) {
    errors.push(`Missing required headers: ${missingHeaders.join(', ')}`);
  }

  // Check for unexpected headers
  const unexpectedHeaders = normalizedHeaders.filter(header => 
    !normalizedExpected.some(expected => expected === header)
  );

  if (unexpectedHeaders.length > 0) {
    warnings.push(`Unexpected headers found: ${unexpectedHeaders.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// Data sanitization
export function sanitizeData<T>(data: T[], validationRules: any): T[] {
  return data.map(row => {
    const sanitized = { ...row };
    
    Object.entries(validationRules).forEach(([field, rules]) => {
      if (sanitized[field as keyof T] !== undefined) {
        // Convert strings to numbers where appropriate
        if ('min' in rules && 'max' in rules) {
          const value = Number(sanitized[field as keyof T]);
          if (!isNaN(value)) {
            // Clamp values to valid range
            sanitized[field as keyof T] = Math.max(rules.min, Math.min(rules.max, value)) as T[keyof T];
          }
        }
        
        // Trim strings
        if (rules.type === 'string' && typeof sanitized[field as keyof T] === 'string') {
          sanitized[field as keyof T] = (sanitized[field as keyof T] as string).trim() as T[keyof T];
        }
      }
    });
    
    return sanitized;
  });
}
