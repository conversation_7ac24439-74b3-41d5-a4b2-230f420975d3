import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';

interface HeatmapProps {
  data?: any[];
  selectedBatter?: string;
  selectedLevel?: string;
}

interface HitData {
  Batter: string;
  ExitSpeed: number;
  Angle: number;
  ContactPositionY: number;
}

interface PositionMetrics {
  hits: HitData[];
  avgEV: number;
  avgLA: number;
  qocScore: number;
}

const BENCHMARKS = {
  'Professional': { optimal: 92, min: 88 },
  'College': { optimal: 85, min: 80 },
  'High School': { optimal: 80, min: 75 }
};

// Calculate gaussian function for heat distribution
const gaussian = (x: number, mean: number, sigma: number) => {
  return Math.exp(-Math.pow(x - mean, 2) / (2 * Math.pow(sigma, 2)));
};

// Get color for heat intensity
const getHeatColor = (intensity: number, exitVelo: number, level: string) => {
  const levelBenchmark = BENCHMARKS[level as keyof typeof BENCHMARKS] || BENCHMARKS.College;

  // Calculate normalized score based on level, but ensure we show all data
  const normalizedScore = Math.min(100, Math.max(0,
    ((exitVelo - levelBenchmark.min) / (levelBenchmark.optimal - levelBenchmark.min)) * 100
  ));

  // Always maintain a minimum intensity to ensure visibility
  const baseIntensity = 0.1; // minimum opacity
  const adjustedIntensity = baseIntensity + (intensity * 0.5); // scale the rest of the intensity

  // Return color based on score ranges, but always with some visibility
  if (normalizedScore >= 80) {
    return `rgba(203, 107, 30, ${adjustedIntensity})`; // hex orange
  } else if (normalizedScore >= 60) {
    return `rgba(234, 179, 8, ${adjustedIntensity})`; // yellow-500
  } else {
    return `rgba(239, 68, 68, ${adjustedIntensity})`; // red-500
  }
};

const ContactHeatmap: React.FC<HeatmapProps> = ({
  data = [],
  selectedBatter = '',
  selectedLevel = 'College'
}) => {
  // Filter data for selected batter
  const batterData = useMemo(() =>
    selectedBatter ? data.filter(hit => hit.Batter === selectedBatter) : [],
    [data, selectedBatter]
  );

  // Calculate metrics for visualization
  const metrics = useMemo(() => {
    if (!batterData.length) return {};

    const grouped = batterData.reduce<Record<string, PositionMetrics>>((acc, hit) => {
      if (!hit.ContactPositionY) return acc;

      const position = Math.round(hit.ContactPositionY * 10) / 10;
      console.log('Raw ContactPositionY:', hit.ContactPositionY);
      console.log('Rounded position:', position);
      console.log('Calculated y coordinate:', 70 - (position * 15));
      if (!acc[position]) {
        acc[position] = {
          hits: [],
          avgEV: 0,
          avgLA: 0,
          qocScore: 0
        };
      }
      acc[position].hits.push(hit);
      return acc;
    }, {});

    // Calculate averages for each position
    Object.keys(grouped).forEach(pos => {
      const hits = grouped[pos].hits;
      grouped[pos].avgEV = hits.reduce((sum, hit) => sum + (hit.ExitSpeed || 0), 0) / hits.length;
      grouped[pos].avgLA = hits.reduce((sum, hit) => sum + (hit.Angle || 0), 0) / hits.length;
      // Using existing QoC calculation
      grouped[pos].qocScore = hits.reduce((sum, hit) => sum + ((hit.ExitSpeed > 95 ? 100 : hit.ExitSpeed / 95 * 100) || 0), 0) / hits.length;
    });

    return grouped;
  }, [batterData]);

  const statistics = useMemo(() => {
    if (!batterData.length) return { avgEV: 0, avgLA: 0, barrelCount: 0 };

    const validHits = batterData.filter(hit => hit.ExitSpeed && hit.Angle);
    const levelOptimal = BENCHMARKS[selectedLevel as keyof typeof BENCHMARKS]?.optimal || 85;

    return {
      avgEV: validHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / validHits.length,
      avgLA: validHits.reduce((sum, hit) => sum + hit.Angle, 0) / validHits.length,
      barrelCount: validHits.filter(hit => hit.ExitSpeed >= levelOptimal).length
    };
  }, [batterData, selectedLevel]);

  if (!selectedBatter) {
    return (
      <div className="w-full h-96 flex items-center justify-center text-gray-500">
        Select a batter to view contact point analysis
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      {/* Gradient Legend */}
      <div className="absolute middle-4 right-4 flex items-center gap-2">
        <div className="h-4 w-20 bg-gradient-to-r from-red-500 via-yellow-500 to-green-500" />
        <div className="text-xs text-black">
          <span>Quality</span>
        </div>
      </div>

      {/* Main Visualization */}
      <div className="relative h-full w-full">
        <svg viewBox="-10 14 120 100" preserveAspectRatio="xMidYMid meet" className="w-full h-[350px]">
          {/* Home Plate */}
          <g transform="translate(2, 15)">
            <path
              d="M35,40 L65,40 L65,55 L50,70 L35,55 Z"
              fill="none"
              stroke="black"
              strokeWidth="0.5"
            />
            <circle cx="35" cy="40" r="0.75" fill="black" />
            <circle cx="65" cy="40" r="0.75" fill="black" />
            <circle cx="65" cy="55" r="0.75" fill="black" />
            <circle cx="50" cy="70" r="0.75" fill="black" />
            <circle cx="35" cy="55" r="0.75" fill="black" />
          </g>

          {/* Y-axis and grid */}
          <g transform="translate(-35, 15)">
          {[-1, 0, 1, 2, 3, 4,].map((value) => (
              <React.Fragment key={`grid-${value}`}>
                <line
                  x1="30" y1={70 - (value * 15)} x2="120" y2={70 - (value * 15)}
                  stroke="#000000" strokeWidth="0.5" strokeDasharray="2"
                  opacity={0.2}
                />
                <text
                  x="25" y={70 - (value * 15)}
                  fontSize="3.5" textAnchor="end" dominantBaseline="middle"
                  fill="#000000"
                >
                  {value}ft
                </text>
              </React.Fragment>
            ))}
            <line x1="30" y1="0" x2="30" y2="100" stroke="black" strokeWidth="0.5" />

            console.log('Position in feet:', positionInFeet);
            console.log('Raw y before calculation:', 70);
            console.log('Scaling factor:', 15);
            console.log('Calculated y:', 70 - (positionInFeet * 15));

            {/* Heat Map Layer */}
            <g>
              {Object.entries(metrics).map(([position, data], i) => {
                const positionInFeet = Number(position);
                // Scale the position to our SVG coordinate system
                // 70 is home plate tip, 15 units per foot, moving up reduces y value
                const y = 70 - (positionInFeet * 16);
                // Add 5 to center the heat band
                const adjustedY = y + 2;

                return (
                  <g key={i}>
                    {Array.from({ length: 50 }).map((_, idx) => {
                      const x = 30 + idx * 2;
                      const heatValue = gaussian(x, 75, 30) * (data.qocScore / 100);

                      return (
                        <rect
                          key={idx}
                          x={x}
                          y={adjustedY}  // Use adjusted Y position
                          width="2"
                          height="3.5"
                          fill={getHeatColor(heatValue, data.avgEV, selectedLevel)}
                          opacity={0.3}
                          className="transition-all duration-300 ease-in-out"
                          onMouseEnter={(e) => {
                            const tooltip = document.getElementById('contact-tooltip');
                            if (tooltip) {
                              tooltip.style.display = 'block';
                              const svgRect = e.currentTarget.closest('svg')?.getBoundingClientRect();
                              const rect = (e.target as HTMLElement).getBoundingClientRect();
                              const relativeX = rect.left - (svgRect?.left || 0) + rect.width;
                              const relativeY = rect.top - (svgRect?.top || 0);

                              tooltip.style.left = `${relativeX + 10}px`;
                              tooltip.style.top = `${relativeY}px`;
                              tooltip.innerHTML = `
                                <div class="p-2">
                                  <div class="font-semibold">Contact Point: ${position}ft</div>
                                  <div>Exit Velo: ${data.avgEV.toFixed(1)} mph</div>
                                  <div>Quality Score: ${data.qocScore.toFixed(1)}</div>
                                  <div>Sample Size: ${data.hits.length}</div>
                                </div>
                              `;
                            }
                          }}
                          onMouseMove={(e) => {
                            const tooltip = document.getElementById('contact-tooltip');
                            if (tooltip) {
                              const svgRect = e.currentTarget.closest('svg')?.getBoundingClientRect();
                              const rect = (e.target as HTMLElement).getBoundingClientRect();
                              const relativeX = rect.left - (svgRect?.left || 0) + rect.width;
                              const relativeY = rect.top - (svgRect?.top || 0);

                              tooltip.style.left = `${relativeX + 10}px`;
                              tooltip.style.top = `${relativeY}px`;
                            }
                          }}
                          onMouseLeave={() => {
                            const tooltip = document.getElementById('contact-tooltip');
                            if (tooltip) {
                              tooltip.style.display = 'none';
                            }
                          }}
                        />
                      );
                    })}
                  </g>
                );
              })}
            </g>
          </g>
        </svg>

        {/* Stats Section */}
        <div className="mt-4 flex justify-around text-center">
          <div>
            <div className="text-sm font-medium text-gray-600">Avg Exit Velo</div>
            <div className={`text-lg font-bold ${
              statistics.avgEV >= BENCHMARKS[selectedLevel as keyof typeof BENCHMARKS]?.optimal ? 'text-orange-600' :
              statistics.avgEV >= BENCHMARKS[selectedLevel as keyof typeof BENCHMARKS]?.min ? 'text-yellow-500' :
              'text-red-500'
            }`}>
              {statistics.avgEV.toFixed(1)} mph
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Avg Launch Angle</div>
            <div className={`text-lg font-bold ${
              (statistics.avgLA >= 10 && statistics.avgLA <= 25) ? 'text-orange-600' :
              (statistics.avgLA >= 5 && statistics.avgLA <= 30) ? 'text-yellow-500' :
              'text-red-500'
            }`}>
              {statistics.avgLA.toFixed(1)}°
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Barrels</div>
            <div className="text-lg font-bold text-blue-500">
              {statistics.barrelCount}
            </div>
          </div>
        </div>
      </div>
      {/* Tooltip container */}
      <div
        id="contact-tooltip"
        className="absolute hidden bg-white border border-gray-200 rounded shadow-lg text-sm text-black z-50 whitespace-nowrap"
      />
    </div>
  );
};

export default ContactHeatmap;