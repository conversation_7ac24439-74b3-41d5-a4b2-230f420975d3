import React, { useState, useMemo } from 'react';
import { <PERSON>, CardHeader, <PERSON>T<PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import {
    calculateQualityOfContactScore,
    calculateRoomForErrorScore,
    calculateLaunchAngleScore,
    calculateTotalHitScore,
    LEVEL_BENCHMARKS,
  } from '@/components/utils/calculateScores';

type MetricType = 'qualityOfContact' | 'roomForError' | 'launchAngle' | 'overall';

interface HittingTrendAnalysisProps {
    data: Array<{
      Batter: string;
      BatterSide: string;
      ExitSpeed: number;
      Angle: number;
      Direction: number;
      HitSpinRate: number;
      ContactPositionY: number;
      HitSpinAxis: number;
      Level: 'Professional' | 'College' | 'High School';
      EventID?: string;
      "Event Date"?: string;
    }>;
    selectedBatter: string;
    selectedLevel: 'Professional' | 'College' | 'High School';
    timeRange?: string;
  }

  interface Metric {
    date: string;
    totalScore: number;
    qualityOfContactScore: number;
    roomForErrorScore: number;
    launchAngleScore: number;
    hits: number;
  }

  const HittingTrendAnalysis: React.FC<HittingTrendAnalysisProps> = ({
    data,
    selectedBatter,
    selectedLevel,
    timeRange
  }) => {
    const [selectedTimeRange, setSelectedTimeRange] = useState<string>("30");
    const [selectedMetric, setSelectedMetric] = useState<string>("all");
    const metrics = useMemo<Metric[]>(() => {
      // First filter by date range
      const currentDate = new Date();
      const batterData = data
        .filter(hit => {
          if (!selectedTimeRange || selectedTimeRange === 'all') return true;
          const hitDate = new Date(hit["Event Date"] || '');
          const daysDiff = Math.floor((currentDate.getTime() - hitDate.getTime()) / (1000 * 60 * 60 * 24));
          return daysDiff <= parseInt(selectedTimeRange);
        })
        .filter(hit => hit.Batter === selectedBatter)
        .map(hit => ({ ...hit, Level: selectedLevel }));

      // If no data in the selected time range, return empty array
      if (batterData.length === 0) {
        return [];
      }

      if (batterData.length === 0) return [];

      const groupedByDate = batterData.reduce((acc, hit) => {
        const date = hit["Event Date"] || 'Unknown';
        if (!acc[date]) acc[date] = [];
        acc[date].push(hit);
        return acc;
      }, {} as Record<string, typeof batterData>);

      return Object.entries(groupedByDate)
        .map(([date, hits]) => {
          const validHits = hits.filter(hit => hit.ExitSpeed && typeof hit.Angle !== 'undefined');

          if (validHits.length === 0) return null;

          console.log('First valid hit:', JSON.stringify(validHits[0], null, 2));
          console.log('Selected level benchmarks:', JSON.stringify(LEVEL_BENCHMARKS[selectedLevel], null, 2));
          const qualityOfContactScore = calculateQualityOfContactScore(
            validHits[0],
            LEVEL_BENCHMARKS[selectedLevel]
          );
          console.log('Quality score inputs:', {
            exitVelo: validHits[0].ExitSpeed,
            spinRate: validHits[0].HitSpinRate,
            spinAxis: validHits[0].HitSpinAxis,
            level: selectedLevel
          });
          console.log('Quality of contact score:', qualityOfContactScore);

          const roomForErrorScore = calculateRoomForErrorScore(
            validHits,
            validHits.length
          );

          const launchAngleScore = calculateLaunchAngleScore(
            validHits[0]
          );

          const totalScore = (qualityOfContactScore * 0.4) +
                            (roomForErrorScore * 0.3) +
                            (launchAngleScore * 0.3);

          return {
            date,
            totalScore,
            qualityOfContactScore,
            roomForErrorScore,
            launchAngleScore,
            hits: validHits.length
          };
        })
        .filter((item): item is NonNullable<typeof item> => item !== null)
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    }, [data, selectedBatter, selectedLevel, selectedTimeRange]);

    // Calculate dynamic domains for better visualization
    const yDomain = useMemo(() => {
      if (metrics.length === 0) return [0, 100];

      // Get all score values
      const allScores = metrics.flatMap(m => [
        Math.min(100, Math.max(0, m.totalScore)), // Clamp between 0 and 100
        Math.min(100, Math.max(0, m.qualityOfContactScore)),
        Math.min(100, Math.max(0, m.roomForErrorScore)),
        Math.min(100, Math.max(0, m.launchAngleScore))
      ]);

      const minScore = Math.max(0, Math.min(...allScores));
      const maxScore = Math.min(100, Math.max(...allScores));

      // Add padding but keep within 0-100 range
      const padding = (maxScore - minScore) * 0.1;
      return [
        Math.max(0, Math.floor(minScore - padding)),
        Math.min(100, Math.ceil(maxScore + padding))
      ];
    }, [metrics]);

    return (
      <div className="w-full">
        <div className="mb-4 flex justify-between items-center">
          <div className="flex gap-4">
            <Select
              value={selectedTimeRange}
              onValueChange={setSelectedTimeRange}
            >
              <SelectTrigger className="select-trigger w-[180px]">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent className="select-content">
                <SelectItem className="select-item" value="7">Last 7 Days</SelectItem>
                <SelectItem className="select-item" value="30">Last 30 Days</SelectItem>
                <SelectItem className="select-item" value="90">Last 90 Days</SelectItem>
                <SelectItem className="select-item" value="all">All Time</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={selectedMetric}
              onValueChange={setSelectedMetric}
            >
              <SelectTrigger className="select-trigger w-[180px]">
                <SelectValue placeholder="Select Metric" />
              </SelectTrigger>
              <SelectContent className="select-content">
                <SelectItem className="select-item" value="all">All Metrics</SelectItem>
                <SelectItem className="select-item" value="totalScore">Total Score</SelectItem>
                <SelectItem className="select-item" value="qualityOfContactScore">Quality of Contact</SelectItem>
                <SelectItem className="select-item" value="roomForErrorScore">Room for Error</SelectItem>
                <SelectItem className="select-item" value="launchAngleScore">Launch Angle</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="h-[600px]">
        <ResponsiveContainer>
          {metrics.length > 0 ? (
            <LineChart
              data={metrics}
              margin={{ top: 20, right: 30, left: 20, bottom: 50 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#cccccc" />
              <XAxis
                dataKey="date"
                tickFormatter={(value) => new Date(value).toLocaleDateString()}
                label={{ value: 'Date', position: 'bottom', offset: 0 }}
              />
              <YAxis
                domain={yDomain}
                label={{ value: 'Score', angle: -90, position: 'insideLeft', offset: 10 }}
              />
              <Tooltip
                formatter={(value: number) => `${value.toFixed(1)}`}
                labelFormatter={(label) => new Date(label).toLocaleDateString()}
              />
              <Line
                type="monotone"
                dataKey="totalScore"
                name="Total Score"
                stroke="#CB6B1E"
                strokeWidth={2}
                dot={{ fill: '#CB6B1E' }}
                hide={selectedMetric !== 'all' && selectedMetric !== 'totalScore'}
              />
              <Line
                type="monotone"
                dataKey="qualityOfContactScore"
                name="Quality of Contact"
                stroke="#E07A2A"
                strokeWidth={2}
                dot={{ fill: '#E07A2A' }}
                hide={selectedMetric !== 'all' && selectedMetric !== 'qualityOfContactScore'}
              />
              <Line
                type="monotone"
                dataKey="roomForErrorScore"
                name="Room for Error"
                stroke="#f59e0b"
                strokeWidth={2}
                dot={{ fill: '#f59e0b' }}
                hide={selectedMetric !== 'all' && selectedMetric !== 'roomForErrorScore'}
              />
              <Line
                type="monotone"
                dataKey="launchAngleScore"
                name="Launch Angle"
                stroke="#fbbf24"
                strokeWidth={2}
                dot={{ fill: '#fbbf24' }}
                hide={selectedMetric !== 'all' && selectedMetric !== 'launchAngleScore'}
              />
            </LineChart>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-300 text-lg">No data available for the selected time range</p>
            </div>
          )}
        </ResponsiveContainer>
        </div>
      </div>
    );
  };

  export default HittingTrendAnalysis;