"use client"

import React, { useState, useMemo, useEffect } from 'react';
import { <PERSON>, Card<PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

// Define types
export interface PitchData {
    Pitcher: string;
    AutoPitchType: string;
    RelSpeed: number;
    SpinRate: number;
    VertBreak: number;
    HorzBreak: number;
    RelSide: number;
    RelHeight: number;
    Extension: number;
    EventID?: string;
    EventDate?: string;
    EventLocation?: string;
    EventType?: string;
    SessionID?: string;
    NextEvent?: string;
  }

  interface PitchTypeMetrics {
    avgVelocity: number;
    avgSpinRate: number;
    avgVertBreak: number;
    avgHorzBreak: number;
    pitchScore?: PitchScore;

    velocitySeparationScore?: number;
    movementScore?: number;
    releasePointScore?: number;
    singlePitchArsenalScore?: number;
  }

  export type PitchTypes = 'FF' | 'SL' | 'CH' | 'CU' | 'Sinker' | 'Sweeper' | 'Splitter' | 'Slurve' | '2-Seam' | '12-6 CB' | 'Gyro Slider';
  type Level = 'Professional' | 'College' | 'High School'

  // Demo data
  const demoData: PitchData[] = [
    // Jake Thompson - Professional
    { Pitcher: 'Jake Thompson', AutoPitchType: 'FF', RelSpeed: 94.2, SpinRate: 2350, VertBreak: 16.2, HorzBreak: 8.1, RelSide: 2.1, RelHeight: 6.2, Extension: 6.8, EventID: 'E001', EventDate: '2024-01-15', EventLocation: 'Training Facility', EventType: 'Baseline Assessment', SessionID: 'S001' },
    { Pitcher: 'Jake Thompson', AutoPitchType: 'FF', RelSpeed: 93.8, SpinRate: 2320, VertBreak: 15.8, HorzBreak: 7.9, RelSide: 2.0, RelHeight: 6.1, Extension: 6.7, EventID: 'E001', EventDate: '2024-01-15', EventLocation: 'Training Facility', EventType: 'Baseline Assessment', SessionID: 'S001' },
    { Pitcher: 'Jake Thompson', AutoPitchType: 'SL', RelSpeed: 87.5, SpinRate: 2580, VertBreak: 1.8, HorzBreak: -6.2, RelSide: 2.1, RelHeight: 6.0, Extension: 6.6, EventID: 'E001', EventDate: '2024-01-15', EventLocation: 'Training Facility', EventType: 'Baseline Assessment', SessionID: 'S001' },
    { Pitcher: 'Jake Thompson', AutoPitchType: 'SL', RelSpeed: 86.9, SpinRate: 2610, VertBreak: 2.1, HorzBreak: -5.8, RelSide: 2.2, RelHeight: 6.1, Extension: 6.5, EventID: 'E001', EventDate: '2024-01-15', EventLocation: 'Training Facility', EventType: 'Baseline Assessment', SessionID: 'S001' },
    { Pitcher: 'Jake Thompson', AutoPitchType: 'CH', RelSpeed: 86.2, SpinRate: 1950, VertBreak: 2.5, HorzBreak: 14.1, RelSide: 2.0, RelHeight: 6.2, Extension: 6.7, EventID: 'E001', EventDate: '2024-01-15', EventLocation: 'Training Facility', EventType: 'Baseline Assessment', SessionID: 'S001' },

    // Follow-up session
    { Pitcher: 'Jake Thompson', AutoPitchType: 'FF', RelSpeed: 95.1, SpinRate: 2380, VertBreak: 16.8, HorzBreak: 8.3, RelSide: 2.1, RelHeight: 6.3, Extension: 6.9, EventID: 'E002', EventDate: '2024-02-01', EventLocation: 'Training Facility', EventType: 'Progress Check', SessionID: 'S002' },
    { Pitcher: 'Jake Thompson', AutoPitchType: 'SL', RelSpeed: 88.2, SpinRate: 2650, VertBreak: 1.5, HorzBreak: -6.8, RelSide: 2.0, RelHeight: 6.1, Extension: 6.7, EventID: 'E002', EventDate: '2024-02-01', EventLocation: 'Training Facility', EventType: 'Progress Check', SessionID: 'S002' },
    { Pitcher: 'Jake Thompson', AutoPitchType: 'CH', RelSpeed: 86.8, SpinRate: 1980, VertBreak: 2.2, HorzBreak: 14.5, RelSide: 2.1, RelHeight: 6.2, Extension: 6.8, EventID: 'E002', EventDate: '2024-02-01', EventLocation: 'Training Facility', EventType: 'Progress Check', SessionID: 'S002' },

    // Mike Rodriguez - College
    { Pitcher: 'Mike Rodriguez', AutoPitchType: 'FF', RelSpeed: 89.1, SpinRate: 2280, VertBreak: 12.5, HorzBreak: 6.2, RelSide: 1.8, RelHeight: 5.9, Extension: 6.3, EventID: 'E003', EventDate: '2024-01-20', EventLocation: 'College Field', EventType: 'Baseline Assessment', SessionID: 'S003' },
    { Pitcher: 'Mike Rodriguez', AutoPitchType: 'FF', RelSpeed: 88.7, SpinRate: 2250, VertBreak: 12.1, HorzBreak: 5.9, RelSide: 1.9, RelHeight: 5.8, Extension: 6.2, EventID: 'E003', EventDate: '2024-01-20', EventLocation: 'College Field', EventType: 'Baseline Assessment', SessionID: 'S003' },
    { Pitcher: 'Mike Rodriguez', AutoPitchType: 'SL', RelSpeed: 81.3, SpinRate: 2450, VertBreak: 0.8, HorzBreak: -5.5, RelSide: 1.8, RelHeight: 5.9, Extension: 6.1, EventID: 'E003', EventDate: '2024-01-20', EventLocation: 'College Field', EventType: 'Baseline Assessment', SessionID: 'S003' },
    { Pitcher: 'Mike Rodriguez', AutoPitchType: 'CU', RelSpeed: 78.5, SpinRate: 2380, VertBreak: -7.2, HorzBreak: -6.8, RelSide: 1.9, RelHeight: 5.8, Extension: 6.0, EventID: 'E003', EventDate: '2024-01-20', EventLocation: 'College Field', EventType: 'Baseline Assessment', SessionID: 'S003' },

    // Alex Chen - High School
    { Pitcher: 'Alex Chen', AutoPitchType: 'FF', RelSpeed: 82.3, SpinRate: 2050, VertBreak: 9.8, HorzBreak: 3.5, RelSide: 1.6, RelHeight: 5.5, Extension: 5.8, EventID: 'E004', EventDate: '2024-01-25', EventLocation: 'High School Field', EventType: 'Baseline Assessment', SessionID: 'S004' },
    { Pitcher: 'Alex Chen', AutoPitchType: 'FF', RelSpeed: 81.9, SpinRate: 2020, VertBreak: 9.5, HorzBreak: 3.2, RelSide: 1.7, RelHeight: 5.4, Extension: 5.7, EventID: 'E004', EventDate: '2024-01-25', EventLocation: 'High School Field', EventType: 'Baseline Assessment', SessionID: 'S004' },
    { Pitcher: 'Alex Chen', AutoPitchType: 'SL', RelSpeed: 74.8, SpinRate: 2200, VertBreak: -0.2, HorzBreak: -4.1, RelSide: 1.6, RelHeight: 5.5, Extension: 5.6, EventID: 'E004', EventDate: '2024-01-25', EventLocation: 'High School Field', EventType: 'Baseline Assessment', SessionID: 'S004' },
    { Pitcher: 'Alex Chen', AutoPitchType: 'CH', RelSpeed: 75.2, SpinRate: 1580, VertBreak: 2.1, HorzBreak: 7.3, RelSide: 1.7, RelHeight: 5.4, Extension: 5.8, EventID: 'E004', EventDate: '2024-01-25', EventLocation: 'High School Field', EventType: 'Baseline Assessment', SessionID: 'S004' },
  ];



   export const ScoreGauge: React.FC<{
    score: number;
    size?: 'sm' | 'md' | 'lg';
    label?: string;
  }> = ({ score, size = 'md', label }) => {
    // Normalize score to be between 0 and 100
    const normalizedScore = Math.min(100, Math.max(0, score));

    // Calculate circle properties
    const radius = size === 'sm' ? 25 : size === 'md' ? 35 : 45;
    const strokeWidth = size === 'sm' ? 4 : size === 'md' ? 6 : 8;
    const circumference = 2 * Math.PI * radius;
    const progress = ((100 - normalizedScore) / 100) * circumference;

    // Determine color based on score
    const getColor = () => {
      if (normalizedScore >= 80) return '#CB6B1E'; // hex orange
      if (normalizedScore >= 60) return '#ffea5a'; // yellow-500
      return '#ef4444'; // red-500
    };

    // Calculate dimensions
    const dimensions = {
      sm: 'w-16 h-16',
      md: 'w-24 h-24',
      lg: 'w-32 h-32',
    }[size];

    const textSize = {
      sm: 'text-lg',
      md: 'text-2xl',
      lg: 'text-3xl',
    }[size];

    const labelSize = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
    }[size];


    return (
      <div className="flex flex-col items-center">
        <div className={`relative ${dimensions} flex items-center justify-center`}>
          <svg className="w-full h-full transform -rotate-90">
            {/* Background circle */}
            <circle
              cx="50%"
              cy="50%"
              r={radius}
              fill="none"
              stroke="#e5e7eb"
              strokeWidth={strokeWidth}
              className="transition-all duration-300"
            />
            {/* Progress circle */}
            <circle
              cx="50%"
              cy="50%"
              r={radius}
              fill="none"
              stroke={getColor()}
              strokeWidth={strokeWidth}
              strokeDasharray={circumference}
              strokeDashoffset={progress}
              className="transition-all duration-300"
              strokeLinecap="round"
            />
          </svg>
          <div className="absolute flex flex-col items-center justify-center">
            <span className={`${textSize} font-bold text-amber-100`}>
              {normalizedScore.toFixed(1)}
            </span>
          </div>
        </div>
        {label && (
          <span className={`${labelSize} text-amber-100 mt-1`}>
            {label}
          </span>
        )}
      </div>
    );
  };

  const LoadingScreen = () => {
    return (
      <>
        <style>
          {`
            .lineAnimation {
              animation: waveUp 1s ease-in-out infinite;
            }
            @keyframes waveUp {
              0%, 100% {
                transform: scaleY(0.2);
              }
              50% {
                transform: scaleY(1);
              }
            }
          `}
        </style>
        <div className="fixed inset-0 bg-white flex items-center justify-center z-50">
          <div className="text-center">
            <div className="w-64 h-64 mx-auto relative">
              <svg viewBox="0 0 100 100" className="w-full h-full">
                {/* Home plate shape */}
                <path
                  d="M20,20 L80,20 L80,50 L50,70 L20,50 Z"
                  fill="none"
                  stroke="black"
                  strokeWidth="2"
                />
                {/* BASELINE text overlaying the line */}
                <line
                  x1="20"
                  y1="20"
                  x2="80"
                  y2="20"
                  stroke="black"
                  strokeWidth="2"
                  />
                {/* BASELINE text overlaying the line */}
                <text
                  x="50"
                  y="20"
                  textAnchor="middle"
                  fontSize="9"
                  fontWeight="bold"
                  fill="white"
                  strokeWidth="4"
                  stroke="white"
                >
                  BASELINE
                </text>
                <text
                  x="50"
                  y="23"
                  textAnchor="middle"
                  fontSize="9"
                  fontWeight="bold"
                >
                  BASELINE
                </text>

                {/* Top dots connecting text */}
                <circle cx="20" cy="20" r="3" fill="black" />
                <circle cx="80" cy="20" r="3" fill="black" />

                {/* Side dots */}
                <circle cx="20" cy="50" r="3" fill="black" />
                <circle cx="80" cy="50" r="3" fill="black" />

                {/* Vertical lines with dots */}
                {[
                  {x: 30, y:51},
                  {x: 40, y:57},
                  {x: 50, y:61},
                  {x: 60, y:57},
                  {x: 70, y:51},
                ].map((pos, i) =>(
                  <g key={i}>
                    <line
                      x1={pos.x}
                      y1={pos.y -25}
                      x2={pos.x}
                      y2={pos.y}
                      stroke="black"
                      strokeWidth="2"
                      className="lineAnimation"
                      style={{
                        animationDelay: `${i * 0.2}s`,
                        transformOrigin: `${pos.x}px ${pos.y}px`
                      }}
                    />
                ))
                    <circle
                      cx={pos.x}
                      cy={pos.y}
                      r="2.5"
                      fill="black"
                    />
                  </g>
                ))}

                {/* Bottom dot */}
                <circle cx="50" cy="70" r="3" fill="black" />
              </svg>
            </div>
            <p className="mt-4 text-lg font-semibold">Loading...</p>
          </div>
        </div>
      </>
    );
  };







  const PitchingMetricsDashboard = (): React.ReactElement => {
  const [data, setData] = useState<PitchData[]>([]);
  const [selectedPitcher, setSelectedPitcher] = useState<string>('');

  const pitchers = useMemo(() => {
    return [...new Set(data.map(pitch => pitch.Pitcher))];
  }, [data]);

  // Load demo data on component mount
  useEffect(() => {
    setData(demoData);
  }, []);

  const pitcherData = useMemo(() => {
    if (!selectedPitcher) return [];
    return data.filter(pitch => pitch.Pitcher === selectedPitcher);
  }, [data, selectedPitcher]);

  // Memoize expensive calculations
  const pitchTypeAverages = useMemo(() => {
    if (!pitcherData.length) return {};

    const pitchTypes = [...new Set(pitcherData.map(pitch => pitch.AutoPitchType))];
    const averages: Record<string, any> = {};

    pitchTypes.forEach(type => {
      const pitches = pitcherData.filter(pitch => pitch.AutoPitchType === type);
      if (pitches.length > 0) {
        averages[type] = {
          AutoPitchType: type,
          RelSpeed: pitches.reduce((sum, pitch) => sum + pitch.RelSpeed, 0) / pitches.length,
          SpinRate: pitches.reduce((sum, pitch) => sum + pitch.SpinRate, 0) / pitches.length,
          VertBreak: pitches.reduce((sum, pitch) => sum + pitch.VertBreak, 0) / pitches.length,
          HorzBreak: pitches.reduce((sum, pitch) => sum + pitch.HorzBreak, 0) / pitches.length,
          RelSide: pitches.reduce((sum, pitch) => sum + pitch.RelSide, 0) / pitches.length,
          RelHeight: pitches.reduce((sum, pitch) => sum + pitch.RelHeight, 0) / pitches.length,
          Extension: pitches.reduce((sum, pitch) => sum + pitch.Extension, 0) / pitches.length,
          Pitcher: pitches[0].Pitcher,
          count: pitches.length
        };
      }
    });

    return averages;
  }, [pitcherData]);

  const pitcherAverages = useMemo(() => {
    if (!pitcherData.length) return { avgRelSide: 0, avgRelHeight: 0 };

    return {
      avgRelSide: pitcherData.reduce((sum, p) => sum + p.RelSide, 0) / pitcherData.length,
      avgRelHeight: pitcherData.reduce((sum, p) => sum + p.RelHeight, 0) / pitcherData.length
    };
  }, [pitcherData]);

  const calculatePitchMetrics = useCallback((averagePitch: any, type: string) => {
    const benchmarks = LEVEL_BENCHMARKS[selectedLevel]?.[type as PitchTypes] || {
      velocity: 0,
      spinRate: 0,
      vertBreak: 0,
      horzBreak: 0
    };

    // Pitch Score
    const singlePitchMetrics = calculateSinglePitchScore(
      averagePitch,
      benchmarks,
      pitcherData,
      DEFAULT_PITCH_SCORE_PARAMS
    );

    // Velocity Separation Score
    const maxDeviationVSS = 13.5;
    const minPercentageVSS = 25;
    const velocityDifference = Math.abs(averagePitch.RelSpeed - benchmarks.velocity);
    const velocitySeparationScore = velocityDifference <= maxDeviationVSS
      ? 100 * Math.pow((minPercentageVSS / 100), (velocityDifference / maxDeviationVSS))
      : 0;

    // Movement Score
    const scalingFactor = 3;
    const verticalBreakDiff = averagePitch.VertBreak - benchmarks.vertBreak;
    const horizontalBreakDiff = averagePitch.HorzBreak - benchmarks.horzBreak;
    const movementDifference = Math.sqrt(
      Math.pow(verticalBreakDiff, 2) + Math.pow(horizontalBreakDiff, 2)
    );
    const referenceMovementDifference = Math.sqrt(
      Math.pow(benchmarks.vertBreak, 2) + Math.pow(benchmarks.horzBreak, 2)
    );
    const movementScore = referenceMovementDifference !== 0
      ? Math.min(((referenceMovementDifference / movementDifference) * 100) + scalingFactor, 100)
      : 0;

    // Release Point Score
    const relSideDiff = Math.abs(averagePitch.RelSide - pitcherAverages.avgRelSide);
    const relHeightDiff = Math.abs(averagePitch.RelHeight - pitcherAverages.avgRelHeight);
    const releasePointScore = Math.min(
      Math.max((1 - ((relSideDiff + relHeightDiff) * 1)) * 100, 0),
      100
    );

    // Single Pitch Arsenal Score
    const singlePitchArsenalScore =
      (velocitySeparationScore * 0.4) +
      (movementScore * 0.3) +
      (releasePointScore * 0.3);

    return {
      pitchScore: singlePitchMetrics,
      velocitySeparationScore,
      movementScore,
      releasePointScore,
      singlePitchArsenalScore
    };
  }, [selectedLevel, pitcherData, pitcherAverages]);

  const arsenalMetrics = useMemo(() => {
    if (!Object.keys(pitchTypeAverages).length) return {} as Record<string, PitchTypeMetrics>;

    const metrics: Record<string, PitchTypeMetrics & { pitchScore: PitchScore }> = {};

    Object.entries(pitchTypeAverages).forEach(([type, averagePitch]) => {
      const calculatedMetrics = calculatePitchMetrics(averagePitch, type);

      metrics[type] = {
        avgVelocity: averagePitch.RelSpeed,
        avgSpinRate: averagePitch.SpinRate,
        avgVertBreak: averagePitch.VertBreak,
        avgHorzBreak: averagePitch.HorzBreak,
        ...calculatedMetrics
      };
    });

    return metrics;
  }, [pitchTypeAverages, calculatePitchMetrics]);

  const arsenalScore: number = useMemo(() => {
    if (!arsenalMetrics) return 0;
    const scores = Object.values(arsenalMetrics).map(
        (metrics) => metrics.singlePitchArsenalScore || 0
    );
    if (scores.length === 0) return 0;
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }, [arsenalMetrics]);

  const getMetricColor = (
    value: number,
    benchmark: number,
    type: 'higher-better' | 'lower-better' = 'higher-better'
  ): string => {
    if (!value || !benchmark) return 'text-gray-600';
    const percentage = (value / benchmark) * 100;
    if (type === 'higher-better') {
      if (percentage >= 110) return 'text-orange-600'; // hex orange
      if (percentage >= 90) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (percentage <= 90) return 'text-orange-600'; // hex orange
      if (percentage <= 110) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const arsenalComparisonData = useMemo(() => {
    if (!Object.keys(arsenalMetrics).length) return [];

    return Object.entries(arsenalMetrics).map(([type, metrics]) => ({
      name: type,
      velocity: metrics.avgVelocity,
      spinRate: metrics.avgSpinRate / 100,
      verticalBreak: metrics.avgVertBreak,
      horizontalBreak: metrics.avgHorzBreak,
    }));
  }, [arsenalMetrics]);

  const calculateMovementDomains = useCallback((data: PitchData[], zoom: number) => {
    if (!data.length) return { x: [-20, 20], y: [-20, 20] };

    const padding = 2;
    const horzBreaks = data.map(d => d.HorzBreak);
    const vertBreaks = data.map(d => d.VertBreak);

    const minX = Math.min(...horzBreaks);
    const maxX = Math.max(...horzBreaks);
    const minY = Math.min(...vertBreaks);
    const maxY = Math.max(...vertBreaks);

    const centerX = (maxX + minX) / 2;
    const centerY = (maxY + minY) / 2;

    const rangeX = (maxX - minX + padding * 2) / zoom;
    const rangeY = (maxY - minY + padding * 2) / zoom;

    return {
      x: [centerX - rangeX/2, centerX + rangeX/2],
      y: [centerY - rangeY/2, centerY + rangeY/2]
    };
  }, []);

  const calculateReleaseDomains = useCallback((data: PitchData[], zoom: number) => {
    if (!data.length) return { x: [0, 4], y: [0, 8] };

    const padding = 0.2;
    const relSides = data.map(d => d.RelSide);
    const relHeights = data.map(d => d.RelHeight);

    const minX = Math.min(...relSides);
    const maxX = Math.max(...relSides);
    const minY = Math.min(...relHeights);
    const maxY = Math.max(...relHeights);

    const centerX = (maxX + minX) / 2;
    const centerY = (maxY + minY) / 2;

    const rangeX = (maxX - minX + padding * 2) / zoom;
    const rangeY = (maxY - minY + padding * 2) / zoom;

    return {
      x: [Math.max(0, centerX - rangeX/2), centerX + rangeX/2],
      y: [Math.max(0, centerY - rangeY/2), centerY + rangeY/2]
    };
  }, []);

  const ZoomControls = ({
    onZoomIn,
    onZoomOut,
    onReset
  }: {
    onZoomIn: () => void;
    onZoomOut: () => void;
    onReset: () => void;
  }) => (
    <div className="absolute top-2 right-2 flex gap-1">
      <Button
        variant="outline"
        size="sm"
        onClick={onZoomIn}
        className="h-8 w-8 p-0 bg-white/80 text-black border-black/20"
      >
        +
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onZoomOut}
        className="h-8 w-8 p-0 bg-white/80 text-black border-black/20"
      >
        -
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onReset}
        className="h-8 w-8 p-0 bg-white/80 text-black border-black/20"
      >
        ↺
      </Button>
    </div>
  );

  const { x: movementXDomain, y: movementYDomain } = useMemo(
    () => calculateMovementDomains(pitcherData, movementZoom),
    [pitcherData, movementZoom]
  );

  const { x: releaseXDomain, y: releaseYDomain } = useMemo(
    () => calculateReleaseDomains(pitcherData, releaseZoom),
    [pitcherData, releaseZoom]
  );


  return (
    <div className="min-h-screen w-full bg-[#f5e1bc]">
      <div className="w-full max-w-7xl mx-auto p-4 space-y-4">
        <Card className="bg-white backdrop-blur shadow-lg">
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-4">
                <h1 className="text-2xl font-bold text-black">Pitching Analytics Dashboard</h1>
                {data.length > 0 && (
                  <span className="text-sm text-black">
                    {data.length} pitches loaded
                  </span>
                )}
              </div>

              {data.length > 0 && (
                <div className="flex gap-4">
                  <Select value={selectedPitcher} onValueChange={setSelectedPitcher}>
                    <SelectTrigger className="w-[200px] bg-white text-black border-black/20">
                      <SelectValue placeholder="Select Pitcher" />
                    </SelectTrigger>
                    <SelectContent className="bg-white text-black">
                      {pitchers.map(pitcher => (
                        <SelectItem key={pitcher} value={pitcher}>
                          {pitcher}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {selectedPitcher && (
                <div className="mt-4">
                  <h2 className="text-xl font-semibold text-black mb-2">
                    {selectedPitcher} - Pitch Data
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {pitcherData.map((pitch, index) => (
                      <Card key={index} className="bg-gray-50">
                        <CardContent className="p-4">
                          <h3 className="font-semibold text-black">{pitch.AutoPitchType}</h3>
                          <p className="text-sm text-gray-600">Velocity: {pitch.RelSpeed} mph</p>
                          <p className="text-sm text-gray-600">Spin Rate: {pitch.SpinRate} rpm</p>
                          <p className="text-sm text-gray-600">Vert Break: {pitch.VertBreak} in</p>
                          <p className="text-sm text-gray-600">Horz Break: {pitch.HorzBreak} in</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default PitchingMetricsDashboard;
