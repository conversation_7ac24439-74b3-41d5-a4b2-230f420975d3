import React, { useMemo, useState } from 'react';

// Add styles for tooltips
const styles = `
  title {
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont;
    font-size: 14px;
    white-space: pre;
  }
`;

interface SprayChartProps {
  data: Array<{
    Batter: string;
    Direction: number;
    Distance: number;
    ExitSpeed: number;
    Angle: number;
  }>;
  selectedBatter: string;
}

interface TooltipProps {
  x: number;
  y: number;
  hit: {
    ExitSpeed: number;
    Angle: number;
    Distance: number;
    Direction: number;
  };
  visible: boolean;
}

const HitTooltip: React.FC<TooltipProps> = ({ x, y, hit, visible }) => {
  if (!visible) return null;

  return (
    <foreignObject
      x={x + 10}
      y={y - 80}
      width={200}
      height={100}
      style={{ pointerEvents: 'none' }}
    >
      <div className="bg-white/90 backdrop-blur p-2 rounded shadow-lg border border-gray-200 text-sm">
        <div className="grid grid-cols-2 gap-1">
          <div className="font-semibold">Exit Velo:</div>
          <div>{hit.ExitSpeed.toFixed(1)} mph</div>
          <div className="font-semibold">Launch Angle:</div>
          <div>{hit.Angle.toFixed(1)}°</div>
          <div className="font-semibold">Distance:</div>
          <div>{hit.Distance} ft</div>
          <div className="font-semibold">Direction:</div>
          <div>{hit.Direction.toFixed(1)}°</div>
        </div>
      </div>
    </foreignObject>
  );
};

const getHitColor = (exitVelo: number, launchAngle: number): string => {
  if (launchAngle >= 10 && launchAngle <= 25 && exitVelo >= 85) {
    return '#CB6B1E';  // hex orange for quality line drives
  }
  if (exitVelo >= 95) {
    return '#eab308';  // yellow-500 for hard hit balls
  }
  if (exitVelo < 75) {
    return '#ef4444';  // red-500 for weak contact
  }
  return '#93c5fd';  // blue-300 for everything else
};

const SprayChart: React.FC<SprayChartProps> = ({ data, selectedBatter }) => {
  const [hoveredHit, setHoveredHit] = useState<{index: number, hit: any} | null>(null);
  const selectedHits = useMemo(() =>
    data.filter(hit => hit.Batter === selectedBatter),
    [data, selectedBatter]
  );

  // Convert direction and distance to x,y coordinates
  const getHitCoordinates = (direction: number, distance: number, exitVelo: number, launchAngle: number) => {
    // Convert distance to a relative scale (assuming 400ft is max viewable distance)
    const maxDistance = 400;
    const scaledDistance = Math.min(distance, maxDistance) / maxDistance;

    // Adjust direction so 0° is center field, -45° is left field line, 45° is right field line
    // First normalize direction to be between -180 and 180
    let adjustedDirection = direction;
    while (adjustedDirection > 180) adjustedDirection -= 360;
    while (adjustedDirection < -180) adjustedDirection += 360;

    // Convert direction to radians
    const directionRad = (adjustedDirection) * (Math.PI / 180);

    // Scale factor determines how far the hits can go
    const scaleFactor = 200;

    // Home plate is at (200, 200)
    const x = 200 + (scaledDistance * scaleFactor * Math.sin(directionRad));
    const y = 200 - (scaledDistance * scaleFactor * Math.cos(directionRad));

    return { x, y };
  };

  return (
    <div className="w-full h-[300px] relative">
      <svg viewBox="-50 -50 500 300" className="w-full h-full">
        <style>{styles}</style>
        {/* Baseball Field */}
        <path
          d="M200,200 L50,50 A 212.13 212.13 0 0 1 350,50 Z"
          fill="#a3e635"
          stroke="#525252"
          strokeWidth="2"
        />
        <path
          d="M200,200 L50,50 A 212.13 212.13 0 0 1 350,50 Z"
          fill="none"
          stroke="#525252"
          strokeWidth="3"
          strokeDasharray="5,5"
          transform="scale(0.75) translate(67,65)"
        />
        <path
          d="M200,200 L50,50 A 212.13 212.13 0 0 1 350,50 Z"
          fill="none"
          stroke="#525252"
          strokeWidth="2"
          strokeDasharray="5,5"
          transform="scale(0.5) translate(200,200)"
        />

        {/* Foul Lines */}
        <line x1="200" y1="200" x2="50" y2="50" stroke="#ffffff" strokeWidth="2" />
        <line x1="200" y1="200" x2="350" y2="50" stroke="#ffffff" strokeWidth="2" />

        {/* Bases */}
        <rect x="195" y="195" width="10" height="10" fill="white" />
        <rect x="145" y="145" width="10" height="10" fill="white" transform="rotate(45, 150, 150)" />
        <rect x="195" y="95" width="10" height="10" fill="white" />
        <rect x="245" y="145" width="10" height="10" fill="white" transform="rotate(45, 250, 150)" />

        {/* Hit Markers */}
        {selectedHits.map((hit, index) => {
            const { x, y } = getHitCoordinates(hit.Direction, hit.Distance, hit.ExitSpeed, hit.Angle);
            return (
              <g key={index}>
                <circle
                  cx={x}
                  cy={y}
                  r={5}
                  fill={getHitColor(hit.ExitSpeed, hit.Angle)}
                  opacity={hoveredHit?.index === index ? 1 : 0.8}
                  className="cursor-pointer transition-opacity duration-200"
                  onMouseEnter={() => setHoveredHit({ index, hit })}
                  onMouseLeave={() => setHoveredHit(null)}
                />
                <HitTooltip
                  x={x}
                  y={y}
                  hit={hit}
                  visible={hoveredHit?.index === index}
                />
              </g>
            );
          })}
      </svg>
    </div>
  );
};

export default SprayChart;