"use client"

import { useState, useEffect } from 'react';
import PitchingMetricsDashboard from '@/components/dashboard/PitchingMetricsDashboard';
import HittingMetricsDashboard from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import { Button } from '@/components/ui/button';
import { CircleDot, Target } from 'lucide-react';
import { useSearchParams } from 'next/navigation';

export default function Home() {
  const [activeDashboard, setActiveDashboard] = useState<'pitching' | 'hitting'>('hitting');
  const searchParams = useSearchParams();

  useEffect(() => {
    const view = searchParams.get('view');
    if (view === 'hitting') {
      setActiveDashboard('hitting');
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen" style={{ background: 'var(--dashboard-bg)' }}>
      {/* Header */}
      <header className="border-b border-gray-700 bg-[#1a1f2e] backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-lg accent-gradient flex items-center justify-center">
                <span className="text-white font-bold text-lg">BA</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  Baseball Analytics
                </h1>
                <p className="text-sm text-gray-400">Performance Dashboard</p>
              </div>
            </div>

            {/* Navigation Tabs */}
            <div className="flex gap-2 bg-[#242938] p-1 rounded-lg">
              <button
                onClick={() => setActiveDashboard('hitting')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all ${
                  activeDashboard === 'hitting'
                    ? 'bg-[#CB6B1E] text-white font-medium'
                    : 'text-gray-300 hover:text-white hover:bg-[#2d3748]'
                }`}
              >
                <Target className="w-4 h-4" />
                Hitting Analytics
              </button>
              <button
                onClick={() => setActiveDashboard('pitching')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all ${
                  activeDashboard === 'pitching'
                    ? 'bg-[#CB6B1E] text-white font-medium'
                    : 'text-gray-300 hover:text-white hover:bg-[#2d3748]'
                }`}
              >
                <CircleDot className="w-4 h-4" />
                Pitching Analytics
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-6">
        {activeDashboard === 'hitting' ? (
          <HittingMetricsDashboard />
        ) : (
          <PitchingMetricsDashboard />
        )}
      </main>
    </div>
  );
}