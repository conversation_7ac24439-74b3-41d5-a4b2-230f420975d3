import { PitchData, PitchTypes, Level } from '../dashboard/PitchingMetricsDashboard';

export interface LevelBenchmark {
  velocity: number;
  spinRate: number;
  vertBreak: number;
  horzBreak: number;
}

export interface PitchScore {
  velocityScore: number;
  movementScore: number;
  spinScore: number;
  releaseScore: number;
  totalScore: number;
}

export interface PitchScoreParams {
  weight_velocity: number;
  weight_movement: number;
  weight_spin: number;
  weight_release_point: number;
  velocity_bonus_factor: number;
  velocity_penalty_factor: number;
  vertical_break_bonus_factor: number;
  vertical_break_penalty_factor: number;
  horizontal_break_bonus_factor: number;
  horizontal_break_penalty_factor: number;
  spin_rate_bonus_factor: number;
  spin_rate_penalty_factor: number;
  release_penalty_factor: number;
  acceptable_release_side_diff: number;
  acceptable_release_height_diff: number;
}

export interface ArsenalScoreParams {
  velocity_separation_minimum: number;
  velocity_separation_penalty: number;
  movement_separation_minimum: number;
  movement_separation_penalty: number;
  release_point_similarity_acceptable: number;
  release_point_similarity_penalty: number;
}

export interface LevelBenchmarks {
  [key: string]: {
    [K in PitchTypes]: LevelBenchmark;
  };
}

export interface PitchTypeWeights {
  [key: string]: {
    weight_velocity: number;
    weight_movement: number;
    weight_spin: number;
    weight_release_point: number;
  };
}

export const LEVEL_BENCHMARKS: LevelBenchmarks = {
  Professional: {
    FF: { velocity: 92, spinRate: 2250, vertBreak: 15, horzBreak: 7 },
    SL: { velocity: 86, spinRate: 2500, vertBreak: 2, horzBreak: -5 },
    CH: { velocity: 85, spinRate: 1900, vertBreak: 2, horzBreak: 13 },
    CU: { velocity: 83, spinRate: 2350, vertBreak: -8, horzBreak: -5 },
    Sinker: {velocity: 89, spinRate: 2350, vertBreak: -8, horzBreak: -5},
    Sweeper: { velocity: 82, spinRate: 2300, vertBreak: -1, horzBreak: -14 },
    Splitter: { velocity: 84, spinRate: 1500, vertBreak: 3, horzBreak: 10 },
    Slurve: { velocity: 78, spinRate: 2150, vertBreak: -6, horzBreak: -12 },
    '2-Seam': { velocity: 90, spinRate: 2100, vertBreak: 6, horzBreak: -8 },
    '12-6 CB': { velocity: 80, spinRate: 2300, vertBreak: -10, horzBreak: -6 },
    'Gyro Slider': { velocity: 87, spinRate: 2150, vertBreak: 2, horzBreak: -4 },
  },
  College: {
    FF: { velocity: 87, spinRate: 2200, vertBreak: 11, horzBreak: 5 },
    SL: { velocity: 80, spinRate: 2400, vertBreak: 1, horzBreak: -5 },
    CH: { velocity: 80, spinRate: 1700, vertBreak: 3, horzBreak: 9 },
    CU: { velocity: 77, spinRate: 2300, vertBreak: -7, horzBreak: -7 },
    Sinker: {velocity: 84, spinRate: 1900, vertBreak: 3, horzBreak: -4},
    Sweeper: { velocity: 77, spinRate: 2250, vertBreak: 0, horzBreak: -10 },
    Splitter: { velocity: 78, spinRate: 1400, vertBreak: -2, horzBreak: 5 },
    Slurve: { velocity: 76, spinRate: 2000, vertBreak: -5, horzBreak: -7 },
    '2-Seam': { velocity: 87, spinRate: 2000, vertBreak: 5, horzBreak: 7 },
    '12-6 CB': { velocity: 77, spinRate: 2250, vertBreak: -9, horzBreak: -3 },
    'Gyro Slider': { velocity: 81, spinRate: 2100, vertBreak: 1, horzBreak: -3 },
  },
  "High School": {
    FF: { velocity: 80, spinRate: 2000, vertBreak: 9, horzBreak: 3 },
    SL: { velocity: 73, spinRate: 2150, vertBreak: 0, horzBreak: -4 },
    CH: { velocity: 74, spinRate: 1500, vertBreak: 2, horzBreak: 7 },
    CU: { velocity: 70, spinRate: 2050, vertBreak: -5, horzBreak: -6 },
    Sinker: {velocity: 76, spinRate: 1800, vertBreak: 2, horzBreak: 7},
    Sweeper: { velocity: 70, spinRate: 2300, vertBreak: -1, horzBreak: -8 },
    Splitter: { velocity: 72, spinRate: 1300, vertBreak: -1, horzBreak: 4 },
    Slurve: { velocity: 69, spinRate: 2200, vertBreak: -4, horzBreak: -6 },
    '2-Seam': { velocity: 78, spinRate: 1650, vertBreak: 4, horzBreak: 6 },
    '12-6 CB': { velocity: 70, spinRate: 2300, vertBreak: -8, horzBreak: -2 },
    'Gyro Slider': { velocity: 75, spinRate: 1750, vertBreak: 0, horzBreak: -2 },
  }
};

export const PITCH_TYPE_WEIGHTS: PitchTypeWeights = {
  'FF': {
    weight_velocity: 0.5,
    weight_movement: 0.4,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  'SL': {
    weight_velocity: 0.4,
    weight_movement: 0.5,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  'CH': {
    weight_velocity: 0.5,
    weight_movement: 0.4,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  'CU': {
    weight_velocity: 0.3,
    weight_movement: 0.6,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  'Sinker': {
    weight_velocity: 0.5,
    weight_movement: 0.4,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  'Sweeper': {
    weight_velocity: 0.3,
    weight_movement: 0.6,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  'Splitter': {
    weight_velocity: 0.3,
    weight_movement: 0.6,
    weight_spin: 0,
    weight_release_point: 0.1
  }
};

export const DEFAULT_PITCH_SCORE_PARAMS: PitchScoreParams = {
  weight_velocity: 0.5,
  weight_movement: 0.4,
  weight_spin: 0,
  weight_release_point: 0.1,
  velocity_bonus_factor: 7.5,
  velocity_penalty_factor: 1.5,
  vertical_break_bonus_factor: 10,
  vertical_break_penalty_factor: 5,
  horizontal_break_bonus_factor: 10,
  horizontal_break_penalty_factor: 5,
  spin_rate_bonus_factor: 0.05,
  spin_rate_penalty_factor: 0.02,
  release_penalty_factor: 1,
  acceptable_release_side_diff: 1,
  acceptable_release_height_diff: 0.5
};

export const DEFAULT_ARSENAL_SCORE_PARAMS: ArsenalScoreParams = {
  velocity_separation_minimum: 6,
  velocity_separation_penalty: 1,
  movement_separation_minimum: 15,
  movement_separation_penalty: 1,
  release_point_similarity_acceptable: 0.5,
  release_point_similarity_penalty: 1
};

// Utility functions
export const calculateVariance = (numbers: number[]): number => {
  const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  const squareDiffs = numbers.map(num => Math.pow(num - mean, 2));
  return squareDiffs.reduce((sum, square) => sum + square, 0) / numbers.length;
};

export const getPitchTypeColor = (pitchType: string): string => {
  const colorMap: { [key: string]: string } = {
    'FF': '#e63946', // Fastball - red
    'SL': '#1d3557', // Slider - navy
    'CH': '#2a9d8f', // Changeup - teal
    'CU': '#6b705c', // Curveball - grey
    'Sinker': '#f4a261', // Sinker - orange
    'Sweeper': '#bc6c25', // Sweeper - brown
    'Splitter': '#588157', // Splitter - green
  };
  return colorMap[pitchType] || '#000000';
};

export const calculateSinglePitchScore = (
  pitch: PitchData,
  benchmarks: LevelBenchmark,
  pitcherData: PitchData[],
  params: PitchScoreParams = DEFAULT_PITCH_SCORE_PARAMS
): PitchScore => {
  // Velocity Score
  const velocityDiff = pitch.RelSpeed - benchmarks.velocity;
  let velocityScore = 50;  // Base score

  if (velocityDiff >= 0) {
    velocityScore = Math.min(100, 70 + (velocityDiff * params.velocity_bonus_factor));
  } else {
    velocityScore = Math.max(0, 70 + (velocityDiff * params.velocity_penalty_factor));
  }

  // Movement Score
  const verticalBreakDiff = pitch.VertBreak - benchmarks.vertBreak;
  const horizontalBreakDiff = pitch.HorzBreak - benchmarks.horzBreak;

  let vbScore = 70;
  if (verticalBreakDiff >= 0) {
    vbScore = Math.min(100, 70 + (verticalBreakDiff * params.vertical_break_bonus_factor));
  } else {
    vbScore = Math.max(0, 70 + (verticalBreakDiff * params.vertical_break_penalty_factor));
  }

  let hbScore = 70;
  if (horizontalBreakDiff >= 0) {
    hbScore = Math.min(100, 70 + (horizontalBreakDiff * params.horizontal_break_bonus_factor));
  } else {
    hbScore = Math.max(0, 70 + (horizontalBreakDiff * params.horizontal_break_penalty_factor));
  }

  const movementScore = (vbScore + hbScore) / 2;

  // Spin Rate Score
  const spinRateDiff = pitch.SpinRate - benchmarks.spinRate;
  let spinScore = 70;
  if (spinRateDiff >= 0) {
    spinScore = Math.min(100, 70 + (spinRateDiff * params.spin_rate_bonus_factor));
  } else {
    spinScore = Math.max(0, 70 + (spinRateDiff * params.spin_rate_penalty_factor));
  }

  // Release Point Score
  const samePitchTypes = pitcherData.filter(p => p.AutoPitchType === pitch.AutoPitchType);
  let releaseScore = 0;

  if (samePitchTypes.length > 0) {
    const meanRelSide = samePitchTypes.reduce((sum, p) => sum + p.RelSide, 0) / samePitchTypes.length;
    const meanRelHeight = samePitchTypes.reduce((sum, p) => sum + p.RelHeight, 0) / samePitchTypes.length;

    const relSideDeviation = Math.abs(pitch.RelSide - meanRelSide);
    const relHeightDeviation = Math.abs(pitch.RelHeight - meanRelHeight);

    if (relSideDeviation <= params.acceptable_release_side_diff &&
        relHeightDeviation <= params.acceptable_release_height_diff) {
      releaseScore = 100;
    } else {
      const penalty = (relSideDeviation + relHeightDeviation) * params.release_penalty_factor;
      releaseScore = Math.max(0, 100 - penalty);
    }
  }

  // Get weights for this pitch type
  const weights = PITCH_TYPE_WEIGHTS[pitch.AutoPitchType] || {
    weight_velocity: 0.5,
    weight_movement: 0.4,
    weight_spin: 0,
    weight_release_point: 0.1,
  };

  // Calculate Total Score
  const totalScore =
    (velocityScore * weights.weight_velocity) +
    (movementScore * weights.weight_movement) +
    (spinScore * weights.weight_spin) +
    (releaseScore * weights.weight_release_point);

  return {
    velocityScore,
    movementScore,
    spinScore,
    releaseScore,
    totalScore
  };
};

export const calculateArsenalScore = (
  pitches: PitchData[],
  params: ArsenalScoreParams = DEFAULT_ARSENAL_SCORE_PARAMS
): number => {
  if (pitches.length < 2) return 0;

  let totalScore = 100;
  const pitchTypes = [...new Set(pitches.map(p => p.AutoPitchType))];

  // Calculate average metrics for each pitch type
  const pitchMetrics = pitchTypes.map(type => {
    const typePitches = pitches.filter(p => p.AutoPitchType === type);
    return {
      type,
      avgVelocity: typePitches.reduce((sum, p) => sum + p.RelSpeed, 0) / typePitches.length,
      avgVertBreak: typePitches.reduce((sum, p) => sum + p.VertBreak, 0) / typePitches.length,
      avgHorzBreak: typePitches.reduce((sum, p) => sum + p.HorzBreak, 0) / typePitches.length,
      avgRelSide: typePitches.reduce((sum, p) => sum + p.RelSide, 0) / typePitches.length,
      avgRelHeight: typePitches.reduce((sum, p) => sum + p.RelHeight, 0) / typePitches.length
    };
  });

  // Velocity Separation
  for (let i = 0; i < pitchMetrics.length; i++) {
    for (let j = i + 1; j < pitchMetrics.length; j++) {
      const velDiff = Math.abs(pitchMetrics[i].avgVelocity - pitchMetrics[j].avgVelocity);
      if (velDiff < params.velocity_separation_minimum) {
        totalScore -= (params.velocity_separation_minimum - velDiff) * params.velocity_separation_penalty;
      }
    }
  }

  // Movement Separation
  for (let i = 0; i < pitchMetrics.length; i++) {
    for (let j = i + 1; j < pitchMetrics.length; j++) {
      const movementDiff = Math.sqrt(
        Math.pow(pitchMetrics[i].avgVertBreak - pitchMetrics[j].avgVertBreak, 2) +
        Math.pow(pitchMetrics[i].avgHorzBreak - pitchMetrics[j].avgHorzBreak, 2)
      );
      if (movementDiff < params.movement_separation_minimum) {
        totalScore -= (params.movement_separation_minimum - movementDiff) * params.movement_separation_penalty;
      }
    }
  }

  // Release Point Similarity
  for (let i = 0; i < pitchMetrics.length; i++) {
    for (let j = i + 1; j < pitchMetrics.length; j++) {
      const releaseDiff = Math.sqrt(
        Math.pow(pitchMetrics[i].avgRelSide - pitchMetrics[j].avgRelSide, 2) +
        Math.pow(pitchMetrics[i].avgRelHeight - pitchMetrics[j].avgRelHeight, 2)
      );
      if (releaseDiff > params.release_point_similarity_acceptable) {
        totalScore -= (releaseDiff - params.release_point_similarity_acceptable) * params.release_point_similarity_penalty;
      }
    }
  }

  return Math.max(0, Math.min(100, totalScore));
};
