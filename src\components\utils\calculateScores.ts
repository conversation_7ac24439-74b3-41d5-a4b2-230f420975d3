// Types and Interfaces
interface QualityOfContactParams {
    weight_ev: number;
    weight_sr: number;
    weight_sa: number;
  }

  interface RoomForErrorParams {
    max_spread_score: number;
    max_ev_score: number;
    max_consistency_score: number;
    max_line_drive_score: number;
    line_drive_min_angle: number;
    line_drive_max_angle: number;
    min_ev_for_line_drive: number;
  }

  interface LaunchAngleParams {
    la_weight: number;
    ev_weight: number;
    la_scaling_factor: number;
    min_la_score: number;
    line_drive_bonus: number;
  }

  interface LevelBenchmarks {
    minOptimalEV: number;
    maxOptimalEV: number;
    belowOptimalStart: number;
    belowOptimalEnd: number;
    optimalStart: number;
    optimalEnd: number;
    aboveAverageStart: number;
    aboveAverageEnd: number;
    perfectStart: number;
    perfectEnd: number;
  }

  type Level = 'Professional' | 'College' | 'High School';

  interface HittingData {
    Batter: string;
    BatterSide: string;
    ExitSpeed: number;
    Angle: number;
    Direction: number;
    HitSpinRate: number;
    ContactPositionY: number;
    HitSpinAxis: number;
    Level: Level;
    EventID?: string;
    "Event Date"?: string;
    "Event Location"?: string;
    "Event Type"?: string;
    "Session ID"?: string;
    "Next Event"?: string;
  }

  // Constants
  export const CONSTANTS = {
    SCORES: {
      MIN_LA_SCORE: 30,
      LA_SCALING_FACTOR: 20,
      EV_SCALING_FACTOR: 10,
      LINE_DRIVE_BONUS: 20,
      MAX_POSSIBLE_STDEV_EV: 20,
      CONSISTENCY_SCALING_CONSTANT: 0.2876820725,
      SPREAD_SCALING_CONSTANT: 0.6931471806,
    },
    WEIGHTS: {
      QUALITY_OF_CONTACT: 0.4,
      ROOM_FOR_ERROR: 0.3,
      LAUNCH_ANGLE: 0.3,
      EV_WEIGHT: 0.7,
      SR_WEIGHT: 0.2,
      SA_WEIGHT: 0.1,
      LA_WEIGHT: 0.8,
      EV_LA_WEIGHT: 0.2
    },
    THRESHOLDS: {
      LINE_DRIVE_MIN_LA: 5,
      LINE_DRIVE_MAX_LA: 20,
      HIGH_EV_BONUS_MIN_LA: 20,
      HIGH_EV_BONUS_MAX_LA: 35,
      HIGH_EV_THRESHOLD: 95,
      MIN_EV_FOR_LINE_DRIVE: 80
    },
    SPIN_RATE: {
      BELOW_OPTIMAL_START: 900,
      BELOW_OPTIMAL_END: 1199,
      OPTIMAL_START: 1200,
      OPTIMAL_END: 2500,
      ABOVE_MAX_START: 2501,
      ABOVE_MAX_END: 2999,
      EXCESSIVE_START: 3000,
      EXCESSIVE_END: 5000
    },
    SPIN_AXIS: {
      OPTIMAL_MIN: 120,
      OPTIMAL_MAX: 240,
      MIN_LIMIT: 0,
      MAX_LIMIT: 360
    },
    COMPONENT_MAXES: {
      SPREAD_SCORE: 30,
      CONSISTENCY_SCORE: 20,
      LINE_DRIVE_SCORE: 20
    }
  };

  export const LEVEL_BENCHMARKS = {
    Professional: {
      minOptimalEV: 92,
      maxOptimalEV: 119,
      belowOptimalStart: 0,
      belowOptimalEnd: 88.99,
      optimalStart: 92,
      optimalEnd: 94.99,
      aboveAverageStart: 95,
      aboveAverageEnd: 97.99,
      perfectStart: 100,
      perfectEnd: 120
    },
    College: {
      minOptimalEV: 85,
      maxOptimalEV: 109,
      belowOptimalStart: 0,
      belowOptimalEnd: 86.99,
      optimalStart: 88,
      optimalEnd: 89.99,
      aboveAverageStart: 90,
      aboveAverageEnd: 92.99,
      perfectStart: 93,
      perfectEnd: 120
    },
    "High School": {
      minOptimalEV: 80,
      maxOptimalEV: 98,
      belowOptimalStart: 0,
      belowOptimalEnd: 78.99,
      optimalStart: 80,
      optimalEnd: 84.99,
      aboveAverageStart: 85,
      aboveAverageEnd: 89.99,
      perfectStart: 90,
      perfectEnd: 120
    }
  } as const;

  // Default parameters based on BHS_Parameters.csv
  export const DEFAULT_PARAMS = {
    qualityOfContact: {
      weight_ev: 0.7,
      weight_sr: 0.2,
      weight_sa: 0.1
    },
    roomForError: {
      max_spread_score: 30,
      max_ev_score: 30,
      max_consistency_score: 20,
      max_line_drive_score: 20,
      line_drive_min_angle: 5,
      line_drive_max_angle: 20,
      min_ev_for_line_drive: 80
    },
    launchAngle: {
      la_weight: 0.8,
      ev_weight: 0.2,
      la_scaling_factor: 20,
      min_la_score: 30,
      line_drive_bonus: 20
    }
  };

  // Calculation Functions
  export const calculateEVScore = (
    exitVelo: number,
    levelBenchmarks: LevelBenchmarks
  ): number => {
    if (!exitVelo) return 0;

    // Below Optimal Range
    if (exitVelo <= levelBenchmarks.belowOptimalEnd) {
      return ((exitVelo - levelBenchmarks.belowOptimalStart) /
        (levelBenchmarks.belowOptimalEnd - levelBenchmarks.belowOptimalStart)) *
        (70 - 50) + 50;
    }

    // Optimal Range
    if (exitVelo <= levelBenchmarks.optimalEnd) {
      return ((exitVelo - levelBenchmarks.optimalStart) /
        (levelBenchmarks.optimalEnd - levelBenchmarks.optimalStart)) *
        (85 - 70) + 70;
    }

    // Above Average Range
    if (exitVelo <= levelBenchmarks.aboveAverageEnd) {
      return ((exitVelo - levelBenchmarks.aboveAverageStart) /
        (levelBenchmarks.aboveAverageEnd - levelBenchmarks.aboveAverageStart)) *
        (100 - 85) + 85;
    }

    // Perfect Range
    return 100;
  };

  export const calculateQualityOfContactScore = (
    hit: HittingData,
    benchmarks: LevelBenchmarks,
    params: QualityOfContactParams = { weight_ev: 0.7, weight_sr: 0.2, weight_sa: 0.1 }
  ): number => {
    // Early return if no ExitSpeed
    if (!hit.ExitSpeed) return 0;

    // Using the exact weights from Google Sheets
    const EV_WEIGHT = 0.7;  // 70%
    const SR_WEIGHT = 0.2;  // 20%
    const SA_WEIGHT = 0.1;  // 10%

    const evScore = calculateEVScore(hit.ExitSpeed, benchmarks);
    const srScore = calculateSpinRateScore(hit.HitSpinRate, hit.ExitSpeed);
    const saScore = calculateSpinAxisScore(hit.HitSpinAxis, hit.ExitSpeed);

    console.log('Individual scores:', { evScore, srScore, saScore });

    // Calculate total score using weights
    const totalScore = (evScore * EV_WEIGHT) +
                      (srScore * SR_WEIGHT) +
                      (saScore * SA_WEIGHT);

    return Math.min(100, Math.max(0, totalScore)); // Ensure score is between 0 and 100
  };

  export const calculateRoomForErrorScore = (
    hits: HittingData[],
    validHits: number
  ): number => {
    if (validHits <= 1) return 0;

    // Component maximum scores from Google Sheets
    const MAX_SPREAD_SCORE = 30;
    const MAX_CONSISTENCY_SCORE = 20;
    const MAX_LINE_DRIVE_SCORE = 20;

    // Calculate spread score
    const contactPositions = hits
      .filter(hit => hit.ContactPositionY)
      .map(hit => hit.ContactPositionY);

    const posYRange = contactPositions.length > 0
      ? Math.max(...contactPositions) - Math.min(...contactPositions)
      : 0;

    // Calculate consistency score using standard deviation
    const validExitSpeeds = hits
      .filter(hit => hit.ExitSpeed)
      .map(hit => hit.ExitSpeed);

    const avgEV = validExitSpeeds.reduce((sum, ev) => sum + ev, 0) / validExitSpeeds.length;
    const evStandardDeviation = Math.sqrt(
      validExitSpeeds.reduce((sum, ev) => sum + Math.pow(ev - avgEV, 2), 0) / validExitSpeeds.length
    );

    // Calculate spread score using exponential formula from Google Sheets
    const SPREAD_SCALING_CONSTANT = -Math.log(15 / MAX_SPREAD_SCORE);
    const spreadScore = MAX_SPREAD_SCORE * (1 - Math.exp(-SPREAD_SCALING_CONSTANT * posYRange));

    // Calculate consistency score using Google Sheets formula
    const MAX_POSSIBLE_STDEV_EV = 20;
    const CONSISTENCY_SCALING_CONSTANT = 0.2876820725;
    const consistencyScore = Math.max(
      MAX_CONSISTENCY_SCORE * (1 - (evStandardDeviation / MAX_POSSIBLE_STDEV_EV) * CONSISTENCY_SCALING_CONSTANT),
      0
    );

    // Calculate line drive score
    const lineDrives = hits.filter(hit =>
      hit.Angle >= 5 &&
      hit.Angle <= 20 &&
      hit.ExitSpeed >= 80
    );

    const lineDriveScore = Math.min(
      (lineDrives.length / validHits) * MAX_LINE_DRIVE_SCORE,
      MAX_LINE_DRIVE_SCORE
    );

    // Return total score capped at 100
    return Math.min(spreadScore + consistencyScore + lineDriveScore, 100);
  };

  export const calculateLaunchAngleScore = (
    hit: HittingData,
    params: LaunchAngleParams = {
      la_weight: 0.8,
      ev_weight: 0.2,
      la_scaling_factor: 20,
      min_la_score: 30,
      line_drive_bonus: 20
    }
  ): number => {
    if (!hit.Angle || !hit.ExitSpeed) return 0;

    // Constants from Google Sheets
    const LA_WEIGHT = 0.8;
    const EV_WEIGHT = 0.2;
    const LINE_DRIVE_MIN_LA = 5;
    const LINE_DRIVE_MAX_LA = 20;
    const HIGH_EV_BONUS_MIN_LA = 20;
    const HIGH_EV_BONUS_MAX_LA = 35;
    const HIGH_EV_THRESHOLD = 95;
    const LINE_DRIVE_BONUS = 20;
    const MIN_LA_SCORE = 30;
    const LA_SCALING_FACTOR = 20;

    // Calculate LA proximity score
    const laProxScore = calculateLaunchAngleProxScore(hit.Angle, {
      la_weight: 0.8,
      ev_weight: 0.2,
      la_scaling_factor: 20,
      min_la_score: 30,
      line_drive_bonus: 20
    });

    // Calculate EV proximity score
    const evProxScore = calculateEVProxScore(hit.ExitSpeed, hit.Level);

    // Combined score
    const combinedScore = (laProxScore * LA_WEIGHT + evProxScore * EV_WEIGHT);

    // Line drive bonus
    const lineDriveBonus = (
      (hit.Angle >= LINE_DRIVE_MIN_LA && hit.Angle <= LINE_DRIVE_MAX_LA && hit.ExitSpeed >= getLevelMinEV(hit.Level)) ||
      (hit.Angle >= HIGH_EV_BONUS_MIN_LA && hit.Angle <= HIGH_EV_BONUS_MAX_LA && hit.ExitSpeed > HIGH_EV_THRESHOLD)
    ) ? LINE_DRIVE_BONUS : 0;

    return Math.min(100, combinedScore + lineDriveBonus);
  };

  // Helper function to get minimum EV for level
  const getLevelMinEV = (level: Level): number => {
    const benchmarks = LEVEL_BENCHMARKS[level];
    return benchmarks ? benchmarks.minOptimalEV : LEVEL_BENCHMARKS.College.minOptimalEV;
  };

  export const calculateTotalHitScore = (
    hit: HittingData,
    validHits: number,
    allHits: HittingData[]
  ): number => {
    console.log('Hit Data:', hit);
    console.log('Valid Hits:', validHits);
    console.log('All Hits:', allHits.length);

    const benchmarks = LEVEL_BENCHMARKS[hit.Level];
    console.log('Level:', hit.Level);
    console.log('Benchmarks:', benchmarks);

    if (!benchmarks || validHits === 0) {
      console.log('Returning 0 due to:', !benchmarks ? 'no benchmarks' : 'no valid hits');
      return 0;
    }

    const qualityOfContactScore = calculateQualityOfContactScore(hit, benchmarks);
    console.log('Quality of Contact Score:', qualityOfContactScore);

    const roomForErrorScore = calculateRoomForErrorScore(allHits, validHits);
    console.log('Room for Error Score:', roomForErrorScore);

    const launchAngleScore = calculateLaunchAngleScore(hit);
    console.log('Launch Angle Score:', launchAngleScore);

    const totalScore = (qualityOfContactScore * 0.4) +
                      (roomForErrorScore * 0.3) +
                      (launchAngleScore * 0.3);

    console.log('Final Total Score:', totalScore);

    return totalScore;
  };

  // Helper functions
  const calculateSpinRateScore = (spinRate: number, exitVelocity: number): number => {
    // Constants from BHS_Parameters
    const RANGES = {
      BELOW_OPTIMAL: { START: 900, END: 1199, START_SCORE: 40, END_SCORE: 80 },
      OPTIMAL: { START: 1200, END: 2500, START_SCORE: 80, END_SCORE: 100 },
      ABOVE_OPTIMAL: { START: 2501, END: 2999, START_SCORE: 100, END_SCORE: 60 },
      EXCESSIVE: { START: 3000, END: 5000, START_SCORE: 60, END_SCORE: 40 }
    };

    // Early return if no spin rate
    if (!spinRate) return 0;

    // Below optimal range
    if (spinRate < RANGES.BELOW_OPTIMAL.START) {
      return RANGES.BELOW_OPTIMAL.START_SCORE;
    }

    // Below optimal to optimal transition
    if (spinRate <= RANGES.BELOW_OPTIMAL.END) {
      const score = RANGES.BELOW_OPTIMAL.START_SCORE +
        ((spinRate - RANGES.BELOW_OPTIMAL.START) /
        (RANGES.BELOW_OPTIMAL.END - RANGES.BELOW_OPTIMAL.START)) *
        (RANGES.BELOW_OPTIMAL.END_SCORE - RANGES.BELOW_OPTIMAL.START_SCORE);
      return Math.min(RANGES.BELOW_OPTIMAL.END_SCORE, Math.max(RANGES.BELOW_OPTIMAL.START_SCORE, score));
    }

    // Optimal range
    if (spinRate <= RANGES.OPTIMAL.END) {
      const score = RANGES.OPTIMAL.START_SCORE +
        ((spinRate - RANGES.OPTIMAL.START) /
        (RANGES.OPTIMAL.END - RANGES.OPTIMAL.START)) *
        (RANGES.OPTIMAL.END_SCORE - RANGES.OPTIMAL.START_SCORE);
      return Math.min(RANGES.OPTIMAL.END_SCORE, Math.max(RANGES.OPTIMAL.START_SCORE, score));
    }

    // Above optimal range (with exit velocity consideration)
    if (spinRate <= RANGES.ABOVE_OPTIMAL.END) {
      // If exit velocity is high enough, maintain optimal score
      if (exitVelocity > 90) {
        return RANGES.OPTIMAL.END_SCORE;
      }
      const score = RANGES.ABOVE_OPTIMAL.START_SCORE -
        ((spinRate - RANGES.ABOVE_OPTIMAL.START) /
        (RANGES.ABOVE_OPTIMAL.END - RANGES.ABOVE_OPTIMAL.START)) *
        (RANGES.ABOVE_OPTIMAL.START_SCORE - RANGES.ABOVE_OPTIMAL.END_SCORE);
      return Math.min(RANGES.ABOVE_OPTIMAL.START_SCORE, Math.max(RANGES.ABOVE_OPTIMAL.END_SCORE, score));
    }

    // Excessive range
    if (spinRate <= RANGES.EXCESSIVE.END) {
      const score = RANGES.EXCESSIVE.START_SCORE -
        ((spinRate - RANGES.EXCESSIVE.START) /
        (RANGES.EXCESSIVE.END - RANGES.EXCESSIVE.START)) *
        (RANGES.EXCESSIVE.START_SCORE - RANGES.EXCESSIVE.END_SCORE);
      return Math.min(RANGES.EXCESSIVE.START_SCORE, Math.max(RANGES.EXCESSIVE.END_SCORE, score));
    }

    // Beyond excessive range
    return RANGES.EXCESSIVE.END_SCORE;
  };

  const calculateSpinAxisScore = (spinAxis: number, exitVelocity: number): number => {
    // Constants based on BHS_Parameters
    const SA_RANGES = {
        OPTIMAL: { MIN: 120, MAX: 240 },
        LIMITS: { MIN: 0, MAX: 360 }
    };

    const SCORES = {
        MIN: 0,
        MAX: 100
    };

    // Early return if no spin axis data (matches IF($F2="", ""))
    if (!spinAxis) return 0;

    // Optimal range check (matches IF($F2 >= Optimal_Min_SA, IF($F2 <= Optimal_Max_SA))
    if (spinAxis >= SA_RANGES.OPTIMAL.MIN && spinAxis <= SA_RANGES.OPTIMAL.MAX) {
        return SCORES.MAX;
    }

    // High exit velocity consideration (matches IF($D2 > 90))
    if (exitVelocity > 90) {
        // Below optimal range
        if (spinAxis < SA_RANGES.OPTIMAL.MIN) {
            const score = SCORES.MAX -
                ((SA_RANGES.OPTIMAL.MIN - spinAxis) /
                (SA_RANGES.OPTIMAL.MIN - SA_RANGES.LIMITS.MIN)) *
                (SCORES.MAX - SCORES.MIN);
            return Math.max(SCORES.MIN, Math.min(SCORES.MAX, score));
        }
        // Above optimal range
        else {
            const score = SCORES.MAX -
                ((spinAxis - SA_RANGES.OPTIMAL.MAX) /
                (SA_RANGES.LIMITS.MAX - SA_RANGES.OPTIMAL.MAX)) *
                (SCORES.MAX - SCORES.MIN);
            return Math.max(SCORES.MIN, Math.min(SCORES.MAX, score));
        }
    }

    // If not optimal and no high exit velocity, return minimum score
    return SCORES.MIN;
};

  const calculateSpreadScore = (
    contactPositionYRange: number,
    validHits: number,
    params: RoomForErrorParams
  ): number => {
    if (validHits <= 0) return 0;

    // Using the exponential formula from your equations
    const spreadScalingConstant = -Math.log(15 / params.max_spread_score);
    return params.max_spread_score * (1 - Math.exp(-spreadScalingConstant * contactPositionYRange));
  };

  const calculateConsistencyScore = (
    evStandardDeviation: number,
    validHits: number,
    params: RoomForErrorParams
  ): number => {
    if (validHits <= 1) return 0;

    // Max possible standard deviation for EV is set to 20 in parameters
    const maxPossibleStdDevEV = 20;
    const consistencyScalingConstant = 0.2876820725; // From parameters

    return Math.max(
      params.max_consistency_score *
      (1 - (evStandardDeviation / maxPossibleStdDevEV) * consistencyScalingConstant),
      0
    );
  };

  const calculateLineDriveScore = (
    hits: HittingData[],
    params: RoomForErrorParams
  ): number => {
    if (hits.length === 0) return 0;

    const lineDrives = hits.filter(hit =>
      hit.Angle >= params.line_drive_min_angle &&
      hit.Angle <= params.line_drive_max_angle &&
      hit.ExitSpeed >= params.min_ev_for_line_drive
    );

    return Math.min(
      (lineDrives.length / hits.length) * params.max_line_drive_score,
      params.max_line_drive_score
    );
  };

  const calculateLaunchAngleProxScore = (
    angle: number,
    params: LaunchAngleParams
  ): number => {
    const lineDriveMinLA = 5;
    const lineDriveMaxLA = 20;

    if (!angle) return 0;

    if (angle >= lineDriveMinLA && angle <= lineDriveMaxLA) {
      return 100;
    }

    if (angle < lineDriveMinLA) {
      return Math.max(
        params.min_la_score,
        100 - ((lineDriveMinLA - angle) / params.la_scaling_factor) * 100
      );
    } else {
      return Math.max(
        params.min_la_score,
        100 - ((angle - lineDriveMaxLA) / params.la_scaling_factor) * 100
      );
    }
  };

  const calculateEVProxScore = (
      exitVelocity: number,
      level: Level
  ): number => {
    const MIN_LA_SCORE = 30;
    const EV_SCALING_FACTOR = 10;

    const benchmarks = LEVEL_BENCHMARKS[level] || LEVEL_BENCHMARKS.College;
    const { minOptimalEV: evMin, maxOptimalEV: evMax } = benchmarks;

    if (exitVelocity >= evMin && exitVelocity <= evMax) {
      return 100;
    }

    return Math.max(
      MIN_LA_SCORE,
      exitVelocity < evMin
        ? 100 - ((evMin - exitVelocity) / EV_SCALING_FACTOR) * 100
        : 100 - ((exitVelocity - evMax) / EV_SCALING_FACTOR) * 100
    );
  };

  const calculateLineDriveBonus = (
      angle: number,
      exitVelocity: number,
      level: Level,
      params: LaunchAngleParams
  ): number => {
    const lineDriveMinLA = 5;
    const lineDriveMaxLA = 20;
    const highEVBonusMinLA = 20;
    const highEVBonusMaxLA = 35;
    const highEVThreshold = 95;

    // Default to College level if the provided level doesn't exist
    const benchmarks = LEVEL_BENCHMARKS[level] || LEVEL_BENCHMARKS['College'];

    // Check for line drive conditions
    if (
      (angle >= lineDriveMinLA && angle <= lineDriveMaxLA && exitVelocity >= benchmarks.minOptimalEV) ||
      (angle >= highEVBonusMinLA && angle <= highEVBonusMaxLA && exitVelocity > highEVThreshold)
    ) {
      return params.line_drive_bonus;
    }

    return 0;
  };