import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { ScoreGauge } from '../PitchingMetricsDashboard';
import {
  calculateTotalHitScore,
  calculateQualityOfContactScore,
  calculateRoomForErrorScore,
  calculateLaunchAngleScore,
  LEVEL_BENCHMARKS,
  DEFAULT_PARAMS
} from '../../utils/calculateScores';

interface HittingData {
  Batter: string;
  BatterSide: string;
  ExitSpeed: number;
  Angle: number;
  Direction: number;
  HitSpinRate: number;
  HitSpinAxis: number;
  Distance: number;
  Bearing: number;
  HangTime: number;
  Level?: 'Professional' | 'College' | 'High School';
  EventID?: string;
  'Event Date'?: string;
  'Event Location'?: string;
  'Event Type'?: string;
  'Session ID'?: string;
  'Next Event'?: string;
}

interface HittingScoreCardsProps {
  data: HittingData[];
  selectedBatter: string;
  selectedLevel: 'Professional' | 'College' | 'High School';
}

const HittingScoreCards: React.FC<HittingScoreCardsProps> = ({
  data,
  selectedBatter,
  selectedLevel
}) => {
  const batterData = data.filter(hit => hit.Batter === selectedBatter);
  const sampleHit = batterData[0] || data[0];

  const qualityScore = calculateQualityOfContactScore(sampleHit, LEVEL_BENCHMARKS[selectedLevel]);
  const roomForErrorScore = calculateRoomForErrorScore(batterData, batterData.length, DEFAULT_PARAMS.roomForError);
  const launchAngleScore = calculateLaunchAngleScore(sampleHit, DEFAULT_PARAMS.launchAngle);
  const totalScore = calculateTotalHitScore(
    { ...sampleHit, Level: selectedLevel },
    batterData.length,
    batterData.map(hit => ({ ...hit, Level: selectedLevel }))
  );

  // Calculate statistics
  const avgExitSpeed = batterData
    .filter(hit => hit.ExitSpeed)
    .reduce((sum, hit) => sum + hit.ExitSpeed, 0) /
    batterData.filter(hit => hit.ExitSpeed).length || 0;

  const avgSpinRate = batterData
    .filter(hit => hit.HitSpinRate)
    .reduce((sum, hit) => sum + hit.HitSpinRate, 0) /
    batterData.filter(hit => hit.HitSpinRate).length || 0;

  const avgSpinAxis = batterData
    .filter(hit => hit.HitSpinAxis)
    .reduce((sum, hit) => sum + hit.HitSpinAxis, 0) /
    batterData.filter(hit => hit.HitSpinAxis).length || 0;

  const avgLaunchAngle = batterData
    .filter(hit => hit.Angle)
    .reduce((sum, hit) => sum + hit.Angle, 0) /
    batterData.filter(hit => hit.Angle).length || 0;

  const lineDrivePercentage = (batterData.filter(hit =>
    hit.Angle >= 5 && hit.Angle <= 20
  ).length / batterData.length * 100) || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Quality of Contact */}
      <div className="floating-card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Quality of Contact</h3>
        <div className="flex flex-col items-center space-y-4">
          <ScoreGauge
            score={qualityScore}
            size="lg"
            label="Quality Score"
          />
          <div className="grid grid-cols-3 gap-3 w-full text-center">
            <div>
              <p className="text-xs font-medium text-gray-400">EV</p>
              <p className="text-sm font-semibold text-white">
                {avgExitSpeed.toFixed(1)}mph
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-gray-400">Spin Rate</p>
              <p className="text-sm font-semibold text-white">
                {Math.round(avgSpinRate)}rpm
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-gray-400">Spin Axis</p>
              <p className="text-sm font-semibold text-white">
                {Math.round(avgSpinAxis)}°
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Room for Error */}
      <div className="floating-card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Room for Error</h3>
        <div className="flex flex-col items-center space-y-4">
          <ScoreGauge
            score={roomForErrorScore}
            size="lg"
            label="Consistency Score"
          />
        </div>
      </div>

      {/* Launch Angle Profile */}
      <div className="floating-card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Launch Angle Profile</h3>
        <div className="flex flex-col items-center space-y-4">
          <ScoreGauge
            score={launchAngleScore}
            size="lg"
            label="Launch Score"
          />
          <div className="grid grid-cols-2 gap-3 w-full text-center">
            <div>
              <p className="text-xs font-medium text-gray-400">Avg Launch Angle</p>
              <p className="text-sm font-semibold text-white">
                {avgLaunchAngle.toFixed(1)}°
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-gray-400">Line Drive %</p>
              <p className="text-sm font-semibold text-white">
                {lineDrivePercentage.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Overall Hitting Score */}
      <div className="floating-card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Overall Hitting Score</h3>
        <div className="flex flex-col items-center space-y-4">
          <ScoreGauge
            score={totalScore}
            size="lg"
            label="Total Score"
          />
        </div>
      </div>
    </div>
  );
};

export default HittingScoreCards;
