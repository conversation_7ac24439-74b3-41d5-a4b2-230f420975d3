@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Dashboard Theme */
:root {
  --dashboard-bg: #0a0e1a;
  --dashboard-surface: #1a1f2e;
  --dashboard-card: #242938;
  --dashboard-accent: #fef3c7; /* original cream - amber-100 */
  --dashboard-accent-secondary: #fde68a; /* darker cream - amber-200 */
  --dashboard-text-primary: #ffffff;
  --dashboard-text-secondary: #a0a6b8;
  --dashboard-border: #2d3748;
  --dashboard-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --dashboard-shadow-hover: 0 12px 48px rgba(203, 107, 30, 0.25); /* orange shadow */
  --accent-orange: #CB6B1E; /* hex orange */
  --accent-orange-bright: #E07A2A; /* brighter orange for hover */
}

body {
  background: var(--dashboard-bg);
  color: var(--dashboard-text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Floating Card Styles */
.floating-card {
  background: var(--dashboard-card);
  border: 2px solid var(--accent-orange); /* orange border */
  border-radius: 16px;
  box-shadow: var(--dashboard-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.floating-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--dashboard-shadow-hover);
  border-color: var(--accent-orange-bright); /* brighter orange on hover */
}

/* Accent Elements */
.accent-gradient {
  background: linear-gradient(135deg, var(--dashboard-accent) 0%, var(--dashboard-accent-secondary) 100%);
}

.accent-border {
  border-color: var(--dashboard-accent);
}

.accent-text {
  color: var(--dashboard-accent);
}

/* Button Styles */
.btn-primary {
  background: var(--dashboard-accent); /* cream background */
  color: var(--dashboard-bg); /* dark text */
  border: 2px solid var(--accent-orange); /* orange border */
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--dashboard-accent-secondary); /* darker cream on hover */
  border-color: var(--accent-orange-bright); /* brighter orange border on hover */
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(203, 107, 30, 0.3); /* orange shadow */
}

.btn-secondary {
  background: var(--dashboard-surface);
  color: var(--dashboard-text-primary);
  border: 2px solid var(--accent-orange); /* orange border */
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--dashboard-card);
  border-color: var(--accent-orange-bright); /* brighter orange on hover */
}

/* Chart Container */
.chart-container {
  background: var(--dashboard-surface);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid var(--dashboard-border);
}

/* Sidebar Styles */
.sidebar {
  background: var(--dashboard-surface);
  border-right: 1px solid var(--dashboard-border);
  backdrop-filter: blur(10px);
}

/* Navigation */
.nav-item {
  color: var(--dashboard-text-secondary);
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.nav-item:hover {
  background: var(--dashboard-card);
  color: var(--dashboard-text-primary);
}

.nav-item.active {
  background: var(--accent-orange);
  color: var(--dashboard-text-primary);
}

/* Input Styles */
.input-field {
  background: var(--dashboard-surface);
  border: 1px solid var(--dashboard-border);
  border-radius: 8px;
  color: var(--dashboard-text-primary);
  padding: 12px 16px;
  transition: all 0.2s ease;
}

.input-field:focus {
  border-color: var(--accent-orange);
  box-shadow: 0 0 0 3px rgba(203, 107, 30, 0.1); /* orange focus shadow */
  outline: none;
}

/* Select Styles */
.select-trigger {
  background: var(--dashboard-surface);
  border: 1px solid var(--dashboard-border);
  border-radius: 8px;
  color: var(--dashboard-text-primary);
  padding: 12px 16px;
  transition: all 0.2s ease;
}

.select-trigger:hover {
  border-color: var(--accent-orange);
}

.select-content {
  background: var(--dashboard-card);
  border: 1px solid var(--dashboard-border);
  border-radius: 8px;
  box-shadow: var(--dashboard-shadow);
}

.select-item {
  color: var(--dashboard-text-primary);
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.select-item:hover {
  background: var(--accent-orange);
  color: var(--dashboard-text-primary);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dashboard-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--dashboard-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-orange);
}