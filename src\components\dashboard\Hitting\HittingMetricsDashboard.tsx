'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Target, CircleDot } from 'lucide-react';
import { ResponsiveContainer, Scatter<PERSON>hart, CartesianGrid, XAxis, YAxis, <PERSON>lt<PERSON>, Scatter } from 'recharts';
import HittingScoreCards from './HittingScoreCards';
import ContactHeatmap from './ContactHeatmap';
import SprayChart from './SprayChart';
import CoachDashboard from '../CoachDashboard';
import HittingTrendAnalysis from './HittingTrendAnalysis';
import EventTimeline from './EventTimeline';
import { useRouter } from 'next/navigation';

// Define types
interface HittingData {
  Batter: string;
  BatterSide: string;
  ExitSpeed: number;
  Angle: number;
  Direction: number;
  Distance: number;
  HitSpinRate: number;
  ContactPositionY: number;
  HitSpinAxis: number;
  Level: 'Professional' | 'College' | 'High School';
  EventID?: string;
  "Event Date"?: string;
  "Event Location"?: string;
  "Event Type"?: string;
  "Session ID"?: string;
  "Next Event"?: string;
}

// Demo hitting data
const demoHittingData: HittingData[] = [
  // John Smith - College
  { Batter: 'John Smith', BatterSide: 'R', ExitSpeed: 95.2, Angle: 15.3, Direction: 12.5, Distance: 320, HitSpinRate: 2200, ContactPositionY: 0.5, HitSpinAxis: 180, Level: 'College', EventID: 'E001', "Event Date": '2024-01-15', "Event Location": 'Training Facility', "Event Type": 'Practice', "Session ID": 'S001' },
  { Batter: 'John Smith', BatterSide: 'R', ExitSpeed: 88.7, Angle: 8.2, Direction: -5.2, Distance: 280, HitSpinRate: 1800, ContactPositionY: 0.3, HitSpinAxis: 165, Level: 'College', EventID: 'E001', "Event Date": '2024-01-15', "Event Location": 'Training Facility', "Event Type": 'Practice', "Session ID": 'S001' },
  { Batter: 'John Smith', BatterSide: 'R', ExitSpeed: 102.1, Angle: 22.8, Direction: 18.7, Distance: 380, HitSpinRate: 2500, ContactPositionY: 0.7, HitSpinAxis: 195, Level: 'College', EventID: 'E001', "Event Date": '2024-01-15', "Event Location": 'Training Facility', "Event Type": 'Practice', "Session ID": 'S001' },
  { Batter: 'John Smith', BatterSide: 'R', ExitSpeed: 91.3, Angle: 12.1, Direction: 2.3, Distance: 310, HitSpinRate: 2100, ContactPositionY: 0.4, HitSpinAxis: 175, Level: 'College', EventID: 'E001', "Event Date": '2024-01-15', "Event Location": 'Training Facility', "Event Type": 'Practice', "Session ID": 'S001' },
  { Batter: 'John Smith', BatterSide: 'R', ExitSpeed: 97.8, Angle: 18.5, Direction: 8.9, Distance: 350, HitSpinRate: 2300, ContactPositionY: 0.6, HitSpinAxis: 185, Level: 'College', EventID: 'E001', "Event Date": '2024-01-15', "Event Location": 'Training Facility', "Event Type": 'Practice', "Session ID": 'S001' },

  // Sarah Johnson - Professional
  { Batter: 'Sarah Johnson', BatterSide: 'L', ExitSpeed: 98.5, Angle: 16.2, Direction: -8.3, Distance: 340, HitSpinRate: 2400, ContactPositionY: 0.5, HitSpinAxis: 170, Level: 'Professional', EventID: 'E002', "Event Date": '2024-01-20', "Event Location": 'Pro Facility', "Event Type": 'Training', "Session ID": 'S002' },
  { Batter: 'Sarah Johnson', BatterSide: 'L', ExitSpeed: 104.3, Angle: 24.1, Direction: -12.7, Distance: 395, HitSpinRate: 2650, ContactPositionY: 0.8, HitSpinAxis: 190, Level: 'Professional', EventID: 'E002', "Event Date": '2024-01-20', "Event Location": 'Pro Facility', "Event Type": 'Training', "Session ID": 'S002' },
  { Batter: 'Sarah Johnson', BatterSide: 'L', ExitSpeed: 92.1, Angle: 9.8, Direction: -3.2, Distance: 295, HitSpinRate: 1950, ContactPositionY: 0.3, HitSpinAxis: 160, Level: 'Professional', EventID: 'E002', "Event Date": '2024-01-20', "Event Location": 'Pro Facility', "Event Type": 'Training', "Session ID": 'S002' },

  // Mike Davis - High School
  { Batter: 'Mike Davis', BatterSide: 'R', ExitSpeed: 85.3, Angle: 13.7, Direction: 15.2, Distance: 275, HitSpinRate: 1850, ContactPositionY: 0.4, HitSpinAxis: 175, Level: 'High School', EventID: 'E003', "Event Date": '2024-01-25', "Event Location": 'High School Field', "Event Type": 'Practice', "Session ID": 'S003' },
  { Batter: 'Mike Davis', BatterSide: 'R', ExitSpeed: 79.8, Angle: 6.5, Direction: 8.1, Distance: 245, HitSpinRate: 1650, ContactPositionY: 0.2, HitSpinAxis: 155, Level: 'High School', EventID: 'E003', "Event Date": '2024-01-25', "Event Location": 'High School Field', "Event Type": 'Practice', "Session ID": 'S003' },
  { Batter: 'Mike Davis', BatterSide: 'R', ExitSpeed: 88.9, Angle: 19.3, Direction: 22.1, Distance: 315, HitSpinRate: 2050, ContactPositionY: 0.6, HitSpinAxis: 185, Level: 'High School', EventID: 'E003', "Event Date": '2024-01-25', "Event Location": 'High School Field', "Event Type": 'Practice', "Session ID": 'S003' },
];

const getHitColor = (exitVelo: number, launchAngle: number): string => {
  // Ground ball (negative to 10 degrees)
  if (launchAngle < 10) {
    return '#ef4444';  // red-500
  }
  // Line drive (10-25 degrees)
  if (launchAngle >= 10 && launchAngle <= 25) {
    return '#CB6B1E';  // hex orange
  }
  // Fly ball (>25 degrees)
  return '#eab308';  // yellow-500
};

const calculateYPosition = (value: number, minDomain: number, maxDomain: number, height: number) => {
  const domainRange = maxDomain - minDomain;
  const percentage = (maxDomain - value) / domainRange;
  return 40 + (percentage * height);
};

const calculateZoneHeight = (start: number, end: number, minDomain: number, maxDomain: number, height: number) => {
  const startY = calculateYPosition(start, minDomain, maxDomain, height);
  const endY = calculateYPosition(end, minDomain, maxDomain, height);
  return Math.abs(endY - startY);
};

export default function HittingMetricsDashboard(): React.ReactElement {
  const [data, setData] = useState<HittingData[]>([]);
  const [selectedBatter, setSelectedBatter] = useState<string>('');
  const [viewMode, setViewMode] = useState<'player' | 'coach'>('player');
  const [showEventTimeline, setShowEventTimeline] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('College');
  const [showTrendAnalysis, setShowTrendAnalysis] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>("30");
  const router = useRouter();



  // Load demo data on component mount
  useEffect(() => {
    setData(demoHittingData);

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const batterParam = urlParams.get('selectedBatter');
    const levelParam = urlParams.get('selectedLevel');

    if (batterParam) {
      setSelectedBatter(decodeURIComponent(batterParam));
    }
    if (levelParam) {
      setSelectedLevel(levelParam as 'Professional' | 'College' | 'High School');
    }
  }, []);

  const batters = useMemo(() => {
    return [...new Set(data.map(hit => hit.Batter))];
  }, [data]);

  const validData = useMemo(() => {
    if (!selectedBatter) return [];
    return data.filter(hit => hit.Batter === selectedBatter && hit.ExitSpeed && hit.Angle);
  }, [data, selectedBatter]);

  const chartDomains = useMemo(() => {
    if (!validData.length) return { x: [40, 110], y: [-20, 50] };

    const exitSpeeds = validData.map(d => d.ExitSpeed);
    const angles = validData.map(d => d.Angle);

    const evMin = Math.floor(Math.min(...exitSpeeds) - 5);
    const evMax = Math.ceil(Math.max(...exitSpeeds) + 5);
    const laMin = Math.floor(Math.min(...angles) - 5);
    const laMax = Math.ceil(Math.max(...angles) + 5);

    return {
      x: [Math.max(40, evMin), Math.min(110, evMax)],
      y: [Math.max(-20, laMin), Math.min(50, laMax)]
    };
  }, [validData]);



  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="floating-card p-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-white">Hitting Analytics Dashboard</h1>
            {data.length > 0 && (
              <span className="text-sm accent-text font-medium">
                {data.length} hits loaded
              </span>
            )}
          </div>
        </div>
      </div>

      {data.length > 0 && (
        <>
          {/* Controls Section */}
          <div className="floating-card p-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center gap-3">
                <label className="text-sm font-medium text-gray-300">Player:</label>
                <Select value={selectedBatter} onValueChange={setSelectedBatter}>
                  <SelectTrigger className="select-trigger w-[200px]">
                    <SelectValue placeholder="Select Batter" />
                  </SelectTrigger>
                  <SelectContent className="select-content">
                    {batters.map(batter => (
                      <SelectItem key={batter} className="select-item" value={batter}>
                        {batter}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-3">
                <label className="text-sm font-medium text-gray-300">Level:</label>
                <Select
                  value={selectedLevel}
                  onValueChange={(value: 'Professional' | 'College' | 'High School') => setSelectedLevel(value)}
                >
                  <SelectTrigger className="select-trigger w-[200px]">
                    <SelectValue placeholder="Select Level" />
                  </SelectTrigger>
                  <SelectContent className="select-content">
                    <SelectItem className="select-item" value="Professional">Professional</SelectItem>
                    <SelectItem className="select-item" value="College">College</SelectItem>
                    <SelectItem className="select-item" value="High School">High School</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <button
                onClick={() => setViewMode(viewMode === 'player' ? 'coach' : 'player')}
                className="btn-secondary"
              >
                {viewMode === 'player' ? 'Switch to Coach View' : 'Switch to Player View'}
              </button>

              <button
                onClick={() => setShowEventTimeline(!showEventTimeline)}
                className="btn-secondary"
              >
                {showEventTimeline ? 'Hide Event Timeline' : 'Show Event Timeline'}
              </button>
            </div>
          </div>
          {viewMode === 'player' ? (
            selectedBatter ? (
              <>
                {showEventTimeline && (
                  <EventTimeline
                    data={data}
                    selectedBatter={selectedBatter}
                    selectedLevel={selectedLevel}
                    selectedEvent={selectedEvent}
                    onEventSelect={setSelectedEvent}
                  />
                )}

                {/* Trend Analysis Toggle Button */}
                <div className="floating-card p-4">
                  <button
                    onClick={() => {
                      // Use sessionStorage instead of localStorage for temporary data
                      sessionStorage.setItem('hittingData', JSON.stringify(data));
                      sessionStorage.setItem('selectedBatter', selectedBatter);
                      sessionStorage.setItem('selectedLevel', selectedLevel);
                      router.push(`/trend-analysis?batter=${encodeURIComponent(selectedBatter)}`);
                    }}
                    className="btn-primary"
                  >
                    View Trend Analysis
                  </button>
                </div>

                {/* Trend Analysis Component */}
                {showTrendAnalysis && (
                  <div className="col-span-2">
                    <HittingTrendAnalysis
                      data={data}
                      selectedBatter={selectedBatter}
                      selectedLevel={selectedLevel}
                      timeRange={selectedTimeRange}
                    />
                  </div>
                )}
                <HittingScoreCards
                  data={data}
                  selectedBatter={selectedBatter}
                  selectedLevel={selectedLevel}
                />

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Contact Point Analysis */}
                  <div className="floating-card p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">Contact Point Analysis</h3>
                    <div className="w-full h-[400px] flex items-center justify-center">
                      <ContactHeatmap
                        data={data}
                        selectedBatter={selectedBatter}
                        selectedLevel={selectedLevel}
                      />
                    </div>
                  </div>

                  {/* Launch Angle vs Exit Velocity */}
                  <div className="floating-card p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">Launch Angle vs Exit Velocity</h3>
                    <div className="chart-container">
                      {(() => {
                        const filteredHits = data.filter(hit => hit.Batter === selectedBatter);
                        const angles = filteredHits.map(hit => hit.Angle);
                        const minAngle = Math.floor(Math.min(...angles));
                        const maxAngle = Math.ceil(Math.max(...angles));

                        // Add buffer to min/max for better visualization
                        const yMin = Math.min(-12, minAngle - 5);
                        const yMax = Math.max(50, maxAngle + 5);

                        return (
                          <div className="h-[400px] w-full">
                            <ResponsiveContainer>
                              <ScatterChart
                                margin={{ top: 40, right: 20, bottom: 20, left: 40 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" stroke="#000000" opacity={0.15} />

                                {/* Use dynamic calculations for zones */}
                                {/* Ground balls zone */}
                                <rect
                                  x="100"
                                  y={calculateYPosition(10, yMin, yMax, 300)}
                                  width="90%"
                                  height={calculateZoneHeight(yMin, 10, yMin, yMax, 300)}
                                  fill="#ef4444"
                                  fillOpacity={0.1}
                                />
                                {/* Line drives zone */}
                                <rect
                                  x="100"
                                  y={calculateYPosition(25, yMin, yMax, 300)}
                                  width="90%"
                                  height={calculateZoneHeight(10, 25, yMin, yMax, 300)}
                                  fill="#22c55e"
                                  fillOpacity={0.1}
                                />
                                {/* Fly balls zone */}
                                <rect
                                  x="100"
                                  y={40}
                                  width="90%"
                                  height={calculateZoneHeight(25, yMax, yMin, yMax, 300)}
                                  fill="#eab308"
                                  fillOpacity={0.1}
                                />

                                <g>
                                  <text x={60} y={20} fill="#666" fontSize={12}>
                                    Ground Balls (&lt;10°)
                                  </text>
                                  <rect x={40} y={10} width={15} height={15} fill="#ef4444" fillOpacity={0.2} />

                                  <text x={220} y={20} fill="#666" fontSize={12}>
                                    Line Drives (10-25°)
                                  </text>
                                  <rect x={200} y={10} width={15} height={15} fill="#22c55e" fillOpacity={0.2} />

                                  <text x={380} y={20} fill="#666" fontSize={12}>
                                    Fly Balls (&gt;25°)
                                  </text>
                                  <rect x={360} y={10} width={15} height={15} fill="#eab308" fillOpacity={0.2} />
                                </g>

                                <XAxis
                                  type="number"
                                  dataKey="ExitSpeed"
                                  name="Exit Velocity"
                                  unit=" mph"
                                  domain={chartDomains.x}
                                  ticks={Array.from({ length: 6 }, (_, i) => Math.round(chartDomains.x[0] + (chartDomains.x[1] - chartDomains.x[0]) * i / 5))}
                                  label={{ value: 'Exit Velocity (mph)', position: 'bottom', offset: 5 }}
                                />
                                <YAxis
                                  type="number"
                                  dataKey="Angle"
                                  name="Launch Angle"
                                  unit="°"
                                  domain={[yMin, yMax]}
                                  ticks={Array.from({ length: 8 }, (_, i) => yMin + (i * ((yMax - yMin) / 7)))}
                                  tickFormatter={(value) => Math.round(value).toString()}
                                  label={{ value: 'Launch Angle (°)', angle: -90, position: 'left', offset: 15, dy: -59 }}
                                />

                                <Tooltip
                                  cursor={{ strokeDasharray: '3 3' }}
                                  content={({ payload }) => {
                                    if (payload && payload[0]) {
                                      const data = payload[0].payload;
                                      const hitType =
                                        data.Angle < 10 ? 'Ground Ball' :
                                        data.Angle <= 25 ? 'Line Drive' : 'Fly Ball';
                                      return (
                                        <div className="bg-white p-2 border border-gray-200 rounded shadow">
                                          <p className="font-semibold">Exit Velo: {data.ExitSpeed.toFixed(1)} mph</p>
                                          <p className="font-semibold">Launch Angle: {data.Angle.toFixed(1)}°</p>
                                          <p className="text-sm text-gray-600">{hitType}</p>
                                        </div>
                                      );
                                    }
                                    return null;
                                  }}
                                />
                                <Scatter
                                  data={data.filter(hit => hit.Batter === selectedBatter)}
                                  shape={(props: any) => (
                                    <circle
                                      cx={props.cx}
                                      cy={props.cy}
                                      r={5}
                                      fill={getHitColor(props.payload.ExitSpeed, props.payload.Angle)}
                                      style={{ opacity: 0.8 }}
                                    />
                                  )}
                                />
                              </ScatterChart>
                            </ResponsiveContainer>
                          </div>
                        );
                      })()}
                    </div>
                  </div>

                  {/* Spray Chart */}
                  <div className="floating-card p-6 lg:col-span-2">
                    <h3 className="text-xl font-semibold text-white mb-4">Spray Chart</h3>
                    <div className="chart-container">
                      <SprayChart data={data} selectedBatter={selectedBatter} />
                    </div>
                  </div>
                </div>
              </>
            ) : null
          ) : (
            <CoachDashboard
              data={data}
              selectedLevel={selectedLevel}
              onPlayerSelect={(player: string) => setSelectedBatter(player)}
              dataType="hitting"
            />
          )}
        </>
      )}
    </div>
  );
}
